// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/qweb/app.proto

package qweb

type GetAppInfoByIdReq struct {
	// CommonExt ExtInfo = 1;
	AppId           string `protobuf:"bytes,2,opt"`
	NeedVersionInfo int32  `protobuf:"varint,3,opt"`
	_               [0]func()
}

type GetAppInfoByIdRsp struct {
	AppInfo *ApiAppInfo `protobuf:"bytes,2,opt"`
	_       [0]func()
}

type ApiAppInfo struct {
	AppId       string `protobuf:"bytes,1,opt"`
	AppName     string `protobuf:"bytes,2,opt"`
	Icon        string `protobuf:"bytes,3,opt"`
	DownloadUrl string `protobuf:"bytes,4,opt"`
	Version     string `protobuf:"bytes,5,opt"`
	Desc        string `protobuf:"bytes,6,opt"`
	// pub accts = 7;
	Type               int32            `protobuf:"varint,8,opt"`
	BaseLibMiniVersion string           `protobuf:"bytes,9,opt"`
	SubPkgs            []*AppSubPkgInfo `protobuf:"bytes,10,rep"`
	// first = 11;
	Domain *DomainConfig `protobuf:"bytes,12,opt"`
}

type AppSubPkgInfo struct {
	SubPkgName  string `protobuf:"bytes,1,opt"`
	DownloadUrl string `protobuf:"bytes,2,opt"`
	Independent int32  `protobuf:"varint,3,opt"`
	FileSize    int32  `protobuf:"varint,4,opt"`
	_           [0]func()
}

type DomainConfig struct {
	RequestDomain      []string `protobuf:"bytes,1,rep"`
	SocketDomain       []string `protobuf:"bytes,2,rep"`
	UploadFileDomain   []string `protobuf:"bytes,3,rep"`
	DownloadFileDomain []string `protobuf:"bytes,4,rep"`
	BusinessDomain     []string `protobuf:"bytes,5,rep"`
	UdpIpList          []string `protobuf:"bytes,6,rep"`
}
