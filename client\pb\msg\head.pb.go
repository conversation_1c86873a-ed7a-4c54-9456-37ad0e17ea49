// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/msg/head.proto

package msg

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type C2CHead struct {
	ToUin       proto.Option[uint64] `protobuf:"varint,1,opt"`
	FromUin     proto.Option[uint64] `protobuf:"varint,2,opt"`
	CcType      proto.Option[uint32] `protobuf:"varint,3,opt"`
	CcCmd       proto.Option[uint32] `protobuf:"varint,4,opt"`
	AuthPicSig  []byte               `protobuf:"bytes,5,opt"`
	AuthSig     []byte               `protobuf:"bytes,6,opt"`
	AuthBuf     []byte               `protobuf:"bytes,7,opt"`
	ServerTime  proto.Option[uint32] `protobuf:"varint,8,opt"`
	ClientTime  proto.Option[uint32] `protobuf:"varint,9,opt"`
	Rand        proto.Option[uint32] `protobuf:"varint,10,opt"`
	PhoneNumber proto.Option[string] `protobuf:"bytes,11,opt"`
}

type CSHead struct {
	Uin           proto.Option[uint64] `protobuf:"varint,1,opt"`
	Command       proto.Option[uint32] `protobuf:"varint,2,opt"`
	Seq           proto.Option[uint32] `protobuf:"varint,3,opt"`
	Version       proto.Option[uint32] `protobuf:"varint,4,opt"`
	RetryTimes    proto.Option[uint32] `protobuf:"varint,5,opt"`
	ClientType    proto.Option[uint32] `protobuf:"varint,6,opt"`
	Pubno         proto.Option[uint32] `protobuf:"varint,7,opt"`
	Localid       proto.Option[uint32] `protobuf:"varint,8,opt"`
	Timezone      proto.Option[uint32] `protobuf:"varint,9,opt"`
	ClientIp      proto.Option[uint32] `protobuf:"fixed32,10,opt"`
	ClientPort    proto.Option[uint32] `protobuf:"varint,11,opt"`
	ConnIp        proto.Option[uint32] `protobuf:"fixed32,12,opt"`
	ConnPort      proto.Option[uint32] `protobuf:"varint,13,opt"`
	InterfaceIp   proto.Option[uint32] `protobuf:"fixed32,14,opt"`
	InterfacePort proto.Option[uint32] `protobuf:"varint,15,opt"`
	ActualIp      proto.Option[uint32] `protobuf:"fixed32,16,opt"`
	Flag          proto.Option[uint32] `protobuf:"varint,17,opt"`
	Timestamp     proto.Option[uint32] `protobuf:"fixed32,18,opt"`
	Subcmd        proto.Option[uint32] `protobuf:"varint,19,opt"`
	Result        proto.Option[uint32] `protobuf:"varint,20,opt"`
	AppId         proto.Option[uint32] `protobuf:"varint,21,opt"`
	InstanceId    proto.Option[uint32] `protobuf:"varint,22,opt"`
	SessionId     proto.Option[uint64] `protobuf:"varint,23,opt"`
	IdcId         proto.Option[uint32] `protobuf:"varint,24,opt"`
	_             [0]func()
}

type DeltaHead struct {
	TotalLen  proto.Option[uint64] `protobuf:"varint,1,opt"`
	Offset    proto.Option[uint64] `protobuf:"varint,2,opt"`
	AckOffset proto.Option[uint64] `protobuf:"varint,3,opt"`
	Cookie    []byte               `protobuf:"bytes,4,opt"`
	AckCookie []byte               `protobuf:"bytes,5,opt"`
	Result    proto.Option[uint32] `protobuf:"varint,6,opt"`
	Flags     proto.Option[uint32] `protobuf:"varint,7,opt"`
}

type IMHead struct {
	HeadType     proto.Option[uint32] `protobuf:"varint,1,opt"`
	CsHead       *CSHead              `protobuf:"bytes,2,opt"`
	S2CHead      *S2CHead             `protobuf:"bytes,3,opt"`
	HttpconnHead *HttpConnHead        `protobuf:"bytes,4,opt"`
	PaintFlag    proto.Option[uint32] `protobuf:"varint,5,opt"`
	LoginSig     *LoginSig            `protobuf:"bytes,6,opt"`
	DeltaHead    *DeltaHead           `protobuf:"bytes,7,opt"`
	C2CHead      *C2CHead             `protobuf:"bytes,8,opt"`
	_            [0]func()
}

type HttpConnHead struct {
	Uin          proto.Option[uint64] `protobuf:"varint,1,opt"`
	Command      proto.Option[uint32] `protobuf:"varint,2,opt"`
	SubCommand   proto.Option[uint32] `protobuf:"varint,3,opt"`
	Seq          proto.Option[uint32] `protobuf:"varint,4,opt"`
	Version      proto.Option[uint32] `protobuf:"varint,5,opt"`
	RetryTimes   proto.Option[uint32] `protobuf:"varint,6,opt"`
	ClientType   proto.Option[uint32] `protobuf:"varint,7,opt"`
	PubNo        proto.Option[uint32] `protobuf:"varint,8,opt"`
	LocalId      proto.Option[uint32] `protobuf:"varint,9,opt"`
	TimeZone     proto.Option[uint32] `protobuf:"varint,10,opt"`
	ClientIp     proto.Option[uint32] `protobuf:"fixed32,11,opt"`
	ClientPort   proto.Option[uint32] `protobuf:"varint,12,opt"`
	QzhttpIp     proto.Option[uint32] `protobuf:"fixed32,13,opt"`
	QzhttpPort   proto.Option[uint32] `protobuf:"varint,14,opt"`
	SppIp        proto.Option[uint32] `protobuf:"fixed32,15,opt"`
	SppPort      proto.Option[uint32] `protobuf:"varint,16,opt"`
	Flag         proto.Option[uint32] `protobuf:"varint,17,opt"`
	Key          []byte               `protobuf:"bytes,18,opt"`
	CompressType proto.Option[uint32] `protobuf:"varint,19,opt"`
	OriginSize   proto.Option[uint32] `protobuf:"varint,20,opt"`
	ErrorCode    proto.Option[uint32] `protobuf:"varint,21,opt"`
	Redirect     *RedirectMsg         `protobuf:"bytes,22,opt"`
	CommandId    proto.Option[uint32] `protobuf:"varint,23,opt"`
	ServiceCmdid proto.Option[uint32] `protobuf:"varint,24,opt"`
	Oidbhead     *TransOidbHead       `protobuf:"bytes,25,opt"`
}

type LoginSig struct {
	Type proto.Option[uint32] `protobuf:"varint,1,opt"`
	Sig  []byte               `protobuf:"bytes,2,opt"`
}

type RedirectMsg struct {
	LastRedirectIp   proto.Option[uint32] `protobuf:"fixed32,1,opt"`
	LastRedirectPort proto.Option[uint32] `protobuf:"varint,2,opt"`
	RedirectIp       proto.Option[uint32] `protobuf:"fixed32,3,opt"`
	RedirectPort     proto.Option[uint32] `protobuf:"varint,4,opt"`
	RedirectCount    proto.Option[uint32] `protobuf:"varint,5,opt"`
	_                [0]func()
}

type S2CHead struct {
	SubMsgtype proto.Option[uint32] `protobuf:"varint,1,opt"`
	MsgType    proto.Option[uint32] `protobuf:"varint,2,opt"`
	FromUin    proto.Option[uint64] `protobuf:"varint,3,opt"`
	MsgId      proto.Option[uint32] `protobuf:"varint,4,opt"`
	RelayIp    proto.Option[uint32] `protobuf:"fixed32,5,opt"`
	RelayPort  proto.Option[uint32] `protobuf:"varint,6,opt"`
	ToUin      proto.Option[uint64] `protobuf:"varint,7,opt"`
	_          [0]func()
}

type TransOidbHead struct {
	Command     proto.Option[uint32] `protobuf:"varint,1,opt"`
	ServiceType proto.Option[uint32] `protobuf:"varint,2,opt"`
	Result      proto.Option[uint32] `protobuf:"varint,3,opt"`
	ErrorMsg    proto.Option[string] `protobuf:"bytes,4,opt"`
	_           [0]func()
}
