syntax = "proto3";

option go_package = "github.com/Mrs4s/MiraiGo/client/pb";

message SSOReserveField {
  int32 flag = 9;
  string qimei = 12;
  int32 newconn_flag = 14;
  string uid = 16;
  int32 imsi = 18;
  int32 network_type = 19;
  int32 ip_stack_type = 20;
  int32 message_type = 21;
  SsoSecureInfo sec_info = 24;
  int32 sso_ip_origin = 28;
}

message SsoSecureInfo {
  bytes sec_sig = 1;
  bytes sec_device_token = 2;
  bytes sec_extra = 3;
}

message DeviceInfo {
  string bootloader = 1;
  string procVersion = 2;
  string codename = 3;
  string incremental = 4;
  string fingerprint = 5;
  string bootId = 6;
  string androidId = 7;
  string baseBand = 8;
  string innerVersion = 9;
}

message RequestBody {
  repeated ConfigSeq rpt_config_list = 1;
}

message ConfigSeq {
  int32 type = 1;
  int32 version = 2;
}

message D50ReqBody {
  int64 appid = 1;
  int32 maxPkgSize = 2;
  int32 startTime = 3;
  int32 startIndex = 4;
  int32 reqNum = 5;
  repeated int64 uinList = 6;
  int32 reqMusicSwitch = 91001;
  int32 reqMutualmarkAlienation = 101001;
  int32 reqMutualmarkScore = 141001;
  int32 reqKsingSwitch = 151001;
  int32 reqMutualmarkLbsshare = 181001;
}

message ReqDataHighwayHead {
  DataHighwayHead msgBasehead = 1;
  SegHead msgSeghead = 2;
  bytes reqExtendinfo = 3;
  int64 timestamp = 4;
  //LoginSigHead? msgLoginSigHead = 5;
}

message RspDataHighwayHead {
  DataHighwayHead msgBasehead = 1;
  SegHead msgSeghead = 2;
  int32 errorCode = 3;
  int32 allowRetry = 4;
  int32 cachecost = 5;
  int32 htcost = 6;
  bytes rspExtendinfo = 7;
  int64 timestamp = 8;
  int64 range = 9;
  int32 isReset = 10;
}

message DataHighwayHead {
  int32 version = 1;
  string uin = 2;
  string command = 3;
  int32 seq = 4;
  int32 retryTimes = 5;
  int32 appid = 6;
  int32 dataflag = 7;
  int32 commandId = 8;
  string buildVer = 9;
  int32 localeId = 10;
}

message SegHead {
  int32 serviceid = 1;
  int64 filesize = 2;
  int64 dataoffset = 3;
  int32 datalength = 4;
  int32 rtcode = 5;
  bytes serviceticket = 6;
  int32 flag = 7;
  bytes md5 = 8;
  bytes fileMd5 = 9;
  int32 cacheAddr = 10;
  int32 queryTimes = 11;
  int32 updateCacheip = 12;
}

message DeleteMessageRequest {
  repeated MessageItem items = 1;
}

message MessageItem {
  int64 fromUin = 1;
  int64 toUin = 2;
  int32 msgType = 3;
  int32 msgSeq = 4;
  int64 msgUid = 5;
  bytes sig = 7;
}

message SubD4 {
  int64 uin = 1;
}

message Sub8A {
  repeated Sub8AMsgInfo msg_info = 1;
  int32 appId = 2;
  int32 instId = 3;
  int32 longMessageFlag = 4;
  bytes reserved = 5;
}

message Sub8AMsgInfo {
  int64 fromUin = 1;
  int64 toUin = 2;
  int32 msgSeq = 3;
  int64 msgUid = 4;
  int64 msgTime = 5;
  int32 msgRandom = 6;
  int32 pkgNum = 7;
  int32 pkgIndex = 8;
  int32 devSeq = 9;
}

message SubB3 {
  int32 type = 1;
  SubB3AddFrdNotify msgAddFrdNotify = 2;
}

message SubB3AddFrdNotify {
  int64 uin = 1;
  string nick = 5;
}

message Sub44 {
  Sub44FriendSyncMsg friendSyncMsg = 1;
  Sub44GroupSyncMsg groupSyncMsg = 2;
}

message Sub44FriendSyncMsg {
  int64 uin = 1;
  int64 fUin = 2;
  int32 processType = 3;
  int32 time = 4;
  int32 processFlag = 5;
  int32 sourceId = 6;
  int32 sourceSubId = 7;
  repeated string strWording = 8;
}

message Sub44GroupSyncMsg {
  int32 msgType = 1;
  int64 msgSeq = 2;
  int64 grpCode = 3;
  int64 gaCode = 4;
  int64 optUin1 = 5;
  int64 optUin2 = 6;
  bytes msgBuf = 7;
  bytes authKey = 8;
  int32 msgStatus = 9;
  int64 actionUin = 10;
  int64 actionTime = 11;
  int32 curMaxMemCount = 12;
  int32 nextMaxMemCount = 13;
  int32 curMemCount = 14;
  int32 reqSrcId = 15;
  int32 reqSrcSubId = 16;
  int32 inviterRole = 17;
  int32 extAdminNum = 18;
  int32 processFlag = 19;
}

message GroupMemberReqBody {
  int64 groupCode = 1;
  int64 uin = 2;
  bool newClient = 3;
  int32 clientType = 4;
  int32 richCardNameVer = 5;
}

message GroupMemberRspBody {
  int64 groupCode = 1;
  int32 selfRole = 2;
  GroupMemberInfo memInfo = 3;
  bool boolSelfLocationShared = 4;
  int32 groupType = 5;
}

message GroupMemberInfo {
  int64 uin = 1;
  int32 result = 2;
  bytes errmsg = 3;
  bool IsFriend = 4;
  bytes remark = 5;
  bool IsConcerned = 6;
  int32 credit = 7;
  bytes card = 8;
  int32 sex = 9;
  bytes location = 10;
  bytes nick = 11;
  int32 age = 12;
  bytes lev = 13;
  int64 join = 14;
  int64 lastSpeak = 15;
  //repeated CustomEntry customEnties = 16;
  //repeated GBarInfo gbarConcerned = 17;
  bytes gbarTitle = 18;
  bytes gbarUrl = 19;
  int32 gbarCnt = 20;
  bool isAllowModCard = 21;
  bool isVip = 22;
  bool isYearVip = 23;
  bool isSuperVip = 24;
  bool isSuperQq = 25;
  int32 vipLev = 26;
  int32 role = 27;
  bool locationShared = 28;
  int64 int64Distance = 29;
  int32 concernType = 30;
  bytes specialTitle = 31;
  int32 specialTitleExpireTime = 32;
  //FlowersEntry flowerEntry = 33;
  //TeamEntry teamEntry = 34;
  bytes phoneNum = 35;
  bytes job = 36;
  int32 medalId = 37;

  int32 level = 39;

  string honor = 41;
}

