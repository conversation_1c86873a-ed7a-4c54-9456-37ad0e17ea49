syntax = "proto2";

option go_package = "github.com/Mrs4s/MiraiGo/client/pb/oidb";

message CreateFolderReqBody {
  optional uint64 groupCode = 1;
  optional uint32 appId = 2;
  optional string parentFolderId = 3;
  optional string folderName = 4;
}

message CreateFolderRspBody {
  optional int32 retCode = 1;
  optional string retMsg = 2;
  optional string clientWording = 3;
  // optional group_file_common.FolderInfo folderInfo = 4;
}

message DeleteFolderReqBody {
  optional uint64 groupCode = 1;
  optional uint32 appId = 2;
  optional string folderId = 3;
}

message DeleteFolderRspBody {
  optional int32 retCode = 1;
  optional string retMsg = 2;
  optional string clientWording = 3;
}

message MoveFolderReqBody {
  optional uint64 groupCode = 1;
  optional uint32 appId = 2;
  optional string folderId = 3;
  optional string parentFolderId = 4;
  optional string destFolderId = 5;
}

message MoveFolderRspBody {
  optional int32 retCode = 1;
  optional string retMsg = 2;
  optional string clientWording = 3;
  // optional group_file_common.FolderInfo folderInfo = 4;
}

message RenameFolderReqBody {
  optional uint64 groupCode = 1;
  optional uint32 appId = 2;
  optional string folderId = 3;
  optional string newFolderName = 4;
}

message RenameFolderRspBody {
  optional int32 retCode = 1;
  optional string retMsg = 2;
  optional string clientWording = 3;
  // optional group_file_common.FolderInfo folderInfo = 4;
}

message D6D7ReqBody {
  optional CreateFolderReqBody createFolderReq = 1;
  optional DeleteFolderReqBody deleteFolderReq = 2;
  optional RenameFolderReqBody renameFolderReq = 3;
  // optional MoveFolderReqBody moveFolderReq = 4;
}

message D6D7RspBody {
  optional CreateFolderRspBody createFolderRsp = 1;
  optional DeleteFolderRspBody deleteFolderRsp = 2;
  optional RenameFolderRspBody renameFolderRsp = 3;
  // optional MoveFolderRspBody moveFolderRsp = 4;
}