// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/msg/report.proto

package msg

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type PbMsgReadedReportReq struct {
	GrpReadReport []*PbGroupReadedReportReq   `protobuf:"bytes,1,rep"`
	DisReadReport []*PbDiscussReadedReportReq `protobuf:"bytes,2,rep"`
	C2CReadReport *PbC2CReadedReportReq       `protobuf:"bytes,3,opt"` //optional PbBindUinMsgReadedConfirmReq bindUinReadReport = 4;
}

type PbMsgReadedReportResp struct {
	GrpReadReport []*PbGroupReadedReportResp   `protobuf:"bytes,1,rep"`
	DisReadReport []*PbDiscussReadedReportResp `protobuf:"bytes,2,rep"`
	C2CReadReport *PbC2CReadedReportResp       `protobuf:"bytes,3,opt"` //optional PbBindUinMsgReadedConfirmResp bindUinReadReport = 4;
}

type PbGroupReadedReportReq struct {
	GroupCode   proto.Option[uint64] `protobuf:"varint,1,opt"`
	LastReadSeq proto.Option[uint64] `protobuf:"varint,2,opt"`
	_           [0]func()
}

type PbDiscussReadedReportReq struct {
	ConfUin     proto.Option[uint64] `protobuf:"varint,1,opt"`
	LastReadSeq proto.Option[uint64] `protobuf:"varint,2,opt"`
	_           [0]func()
}

type PbC2CReadedReportReq struct {
	SyncCookie []byte             `protobuf:"bytes,1,opt"`
	PairInfo   []*UinPairReadInfo `protobuf:"bytes,2,rep"`
}

type UinPairReadInfo struct {
	PeerUin      proto.Option[uint64] `protobuf:"varint,1,opt"`
	LastReadTime proto.Option[uint32] `protobuf:"varint,2,opt"`
	CrmSig       []byte               `protobuf:"bytes,3,opt"`
	PeerType     proto.Option[uint32] `protobuf:"varint,4,opt"`
	ChatType     proto.Option[uint32] `protobuf:"varint,5,opt"`
	Cpid         proto.Option[uint64] `protobuf:"varint,6,opt"`
	AioType      proto.Option[uint32] `protobuf:"varint,7,opt"`
	ToTinyId     proto.Option[uint64] `protobuf:"varint,9,opt"`
}

type PbGroupReadedReportResp struct {
	Result      proto.Option[uint32] `protobuf:"varint,1,opt"`
	Errmsg      proto.Option[string] `protobuf:"bytes,2,opt"`
	GroupCode   proto.Option[uint64] `protobuf:"varint,3,opt"`
	MemberSeq   proto.Option[uint64] `protobuf:"varint,4,opt"`
	GroupMsgSeq proto.Option[uint64] `protobuf:"varint,5,opt"`
	_           [0]func()
}

type PbDiscussReadedReportResp struct {
	Result    proto.Option[uint32] `protobuf:"varint,1,opt"`
	Errmsg    proto.Option[string] `protobuf:"bytes,2,opt"`
	ConfUin   proto.Option[uint64] `protobuf:"varint,3,opt"`
	MemberSeq proto.Option[uint64] `protobuf:"varint,4,opt"`
	ConfSeq   proto.Option[uint64] `protobuf:"varint,5,opt"`
	_         [0]func()
}

type PbC2CReadedReportResp struct {
	Result     proto.Option[uint32] `protobuf:"varint,1,opt"`
	Errmsg     proto.Option[string] `protobuf:"bytes,2,opt"`
	SyncCookie []byte               `protobuf:"bytes,3,opt"`
}
