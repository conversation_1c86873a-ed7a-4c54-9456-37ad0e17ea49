// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0xe07.proto

package oidb

type DE07ReqBody struct {
	Version    int32       `protobuf:"varint,1,opt"`
	Client     int32       `protobuf:"varint,2,opt"`
	Entrance   int32       `protobuf:"varint,3,opt"`
	OcrReqBody *OCRReqBody `protobuf:"bytes,10,opt"`
	_          [0]func()
}

type OCRReqBody struct {
	ImageUrl              string `protobuf:"bytes,1,opt"`
	LanguageType          string `protobuf:"bytes,2,opt"`
	Scene                 string `protobuf:"bytes,3,opt"`
	OriginMd5             string `protobuf:"bytes,10,opt"`
	AfterCompressMd5      string `protobuf:"bytes,11,opt"`
	AfterCompressFileSize int32  `protobuf:"varint,12,opt"`
	AfterCompressWeight   int32  `protobuf:"varint,13,opt"`
	AfterCompressHeight   int32  `protobuf:"varint,14,opt"`
	IsCut                 bool   `protobuf:"varint,15,opt"`
	_                     [0]func()
}

type DE07RspBody struct {
	RetCode    int32       `protobuf:"varint,1,opt"`
	ErrMsg     string      `protobuf:"bytes,2,opt"`
	Wording    string      `protobuf:"bytes,3,opt"`
	OcrRspBody *OCRRspBody `protobuf:"bytes,10,opt"`
	_          [0]func()
}

type TextDetection struct {
	DetectedText string   `protobuf:"bytes,1,opt"`
	Confidence   int32    `protobuf:"varint,2,opt"`
	Polygon      *Polygon `protobuf:"bytes,3,opt"`
	AdvancedInfo string   `protobuf:"bytes,4,opt"`
	_            [0]func()
}

type Polygon struct {
	Coordinates []*Coordinate `protobuf:"bytes,1,rep"`
}

type Coordinate struct {
	X int32 `protobuf:"varint,1,opt"`
	Y int32 `protobuf:"varint,2,opt"`
	_ [0]func()
}

type Language struct {
	Language     string `protobuf:"bytes,1,opt"`
	LanguageDesc string `protobuf:"bytes,2,opt"`
	_            [0]func()
}

type OCRRspBody struct {
	TextDetections           []*TextDetection `protobuf:"bytes,1,rep"`
	Language                 string           `protobuf:"bytes,2,opt"`
	RequestId                string           `protobuf:"bytes,3,opt"`
	OcrLanguageList          []string         `protobuf:"bytes,101,rep"`
	DstTranslateLanguageList []string         `protobuf:"bytes,102,rep"`
	LanguageList             []*Language      `protobuf:"bytes,103,rep"`
	AfterCompressWeight      int32            `protobuf:"varint,111,opt"`
	AfterCompressHeight      int32            `protobuf:"varint,112,opt"`
}
