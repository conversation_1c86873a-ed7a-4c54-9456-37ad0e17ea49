syntax = "proto2";

option go_package = "github.com/Mrs4s/MiraiGo/client/pb/oidb";

message D5EBReqBody {
  repeated uint64 uins = 1;
  optional uint32 max_package_size = 3;
  repeated bytes openid = 4;
  optional uint32 appid = 5;
  optional uint32 reqNick = 20002;
  optional uint32 reqCountry = 20003;
  optional int32 reqProvince = 20004;
  optional int32 reqGender = 20009;
  optional int32 reqAllow = 20014;
  optional int32 reqFaceId = 20015;
  optional int32 reqCity = 20020;
  optional int32 reqConstellation = 20022;
  optional int32 reqCommonPlace1 = 20027;
  optional int32 reqMss3Bitmapextra = 20030;
  optional int32 reqBirthday = 20031;
  optional int32 reqCityId = 20032;
  optional int32 reqLang1 = 20033;
  optional int32 reqLang2 = 20034;
  optional int32 reqLang3 = 20035;
  optional int32 reqAge = 20037;
  optional int32 reqCityZoneId = 20041;
  optional int32 reqOin = 20056;
  optional int32 reqBubbleId = 20059;
  optional int32 reqMss2Identity = 21001;
  optional int32 reqMss1Service = 21002;
  optional int32 reqLflag = 21003;
  optional int32 reqExtFlag = 21004;
  optional int32 reqBasicSvrFlag = 21006;
  optional int32 reqBasicCliFlag = 21007;
  optional int32 reqFullBirthday = 26004;
  optional int32 reqFullAge = 26005;
  optional int32 reqSimpleUpdateTime = 26010;
  optional int32 reqMssUpdateTime = 26011;
  optional int32 reqPstnMultiCallTime = 26012;
  optional int32 reqPstnMultiLastGuideRechargeTime = 26013;
  optional int32 reqPstnC2cCallTime = 26014;
  optional int32 reqPstnC2cLastGuideRechargeTime = 26015;
  optional int32 reqGroupMemCreditFlag = 27022;
  optional int32 reqFaceAddonId = 27025;
  optional int32 reqMusicGene = 27026;
  optional int32 reqStrangerNick = 27034;
  optional int32 reqStrangerDeclare = 27035;
  optional int32 reqLoveStatus = 27036;
  optional int32 reqProfession = 27037;
  optional int32 reqVasColorringFlag = 27041;
  optional int32 reqCharm = 27052;
  optional int32 reqApolloTimestamp = 27059;
  optional int32 reqVasFontIdFlag = 27201;
  optional int32 reqGlobalGroupLevel = 27208;
  optional int32 reqInvite2groupAutoAgreeFlag = 40346;
  optional int32 reqSubaccountDisplayThirdQqFlag = 40348;
  optional int32 notifyPartakeLikeRankingListFlag = 40350;
  optional int32 reqLightalkSwitch = 40506;
  optional int32 reqMusicRingVisible = 40507;
  optional int32 reqMusicRingAutoplay = 40508;
  optional int32 reqMusicRingRedpoint = 40509;
  optional int32 torchDisableFlag = 40525;
  optional int32 reqVasMagicfontFlag = 40530;
  optional int32 reqVipFlag = 41756;
  optional int32 reqAuthFlag = 41783;
  optional int32 reqForbidFlag = 41784;
  optional int32 reqGodForbid = 41804;
  optional int32 reqGodFlag = 41805;
  optional int32 reqCharmLevel = 41950;
  optional int32 reqCharmShown = 41973;
  optional int32 reqFreshnewsNotifyFlag = 41993;
  optional int32 reqApolloVipLevel = 41999;
  optional int32 reqApolloVipFlag = 42003;
  optional int32 reqPstnC2cVip = 42005;
  optional int32 reqPstnMultiVip = 42006;
  optional int32 reqPstnEverC2cVip = 42007;
  optional int32 reqPstnEverMultiVip = 42008;
  optional int32 reqPstnMultiTryFlag = 42011;
  optional int32 reqPstnC2cTryFlag = 42012;
  optional int32 reqSubscribeNearbyassistantSwitch = 42024;
  optional int32 reqTorchbearerFlag = 42051;
  optional int32 preloadDisableFlag = 42073;
  optional int32 reqMedalwallFlag = 42075;
  optional int32 notifyOnLikeRankingListFlag = 42092;
  optional int32 reqApolloStatus = 42980;
}
message D5EBRspBody {
  repeated UdcUinData uinData = 11;
  repeated int64 unfinishedUins = 12;
}
message UdcUinData {
  optional int64 uin = 1;
  optional bytes openid = 4;
  optional bytes nick = 20002;
  optional bytes country = 20003;
  optional bytes province = 20004;
  optional int32 gender = 20009;
  optional int32 allow = 20014;
  optional int32 faceId = 20015;
  optional bytes city = 20020;
  optional int32 constellation = 20022;
  optional int32 commonPlace1 = 20027;
  optional bytes mss3Bitmapextra = 20030;
  optional bytes birthday = 20031;
  optional bytes cityId = 20032;
  optional int32 lang1 = 20033;
  optional int32 lang2 = 20034;
  optional int32 lang3 = 20035;
  optional int32 age = 20037;
  optional int32 cityZoneId = 20041;
  optional int32 oin = 20056;
  optional int32 bubbleId = 20059;
  optional bytes mss2Identity = 21001;
  optional bytes mss1Service = 21002;
  optional int32 lflag = 21003;
  optional int32 extFlag = 21004;
  optional bytes basicSvrFlag = 21006;
  optional  bytes basicCliFlag = 21007;
  optional bytes fullBirthday = 26004;
  optional bytes fullAge = 26005;
  optional int32 simpleUpdateTime = 26010;
  optional int32 mssUpdateTime = 26011;
  optional int32 pstnMultiCallTime = 26012;
  optional int32 pstnMultiLastGuideRechargeTime = 26013;
  optional int32 pstnC2cCallTime = 26014;
  optional int32 pstnC2cLastGuideRechargeTime = 26015;
  optional int32 groupMemCreditFlag = 27022;
  optional int64 faceAddonId = 27025;
  optional bytes musicGene = 27026;
  optional bytes strangerNick = 27034;
  optional bytes strangerDeclare = 27035;
  optional int32 loveStatus = 27036;
  optional int32 profession = 27037;
  optional int32 vasColorringId = 27041;
  optional int32 charm = 27052;
  optional int32 apolloTimestamp = 27059;
  optional int32 vasFontId = 27201;
  optional int32 globalGroupLevel = 27208;
  optional int32 reqInvite2groupAutoAgreeFlag = 40346;
  optional int32 subaccountDisplayThirdQqFlag = 40348;
  optional int32 notifyPartakeLikeRankingListFlag = 40350;
  optional int32 lightalkSwitch = 40506;
  optional int32 musicRingVisible = 40507;
  optional int32 musicRingAutoplay = 40508;
  optional int32 musicRingRedpoint = 40509;
  optional int32 torchDisableFlag = 40525;
  optional int32 vasMagicfontFlag = 40530;
  optional int32 vipFlag = 41756;
  optional int32 authFlag = 41783;
  optional int32 forbidFlag = 41784;
  optional int32 godForbid = 41804;
  optional int32 godFlag = 41805;
  optional int32 charmLevel = 41950;
  optional int32 charmShown = 41973;
  optional int32 freshnewsNotifyFlag = 41993;
  optional int32 apolloVipLevel = 41999;
  optional int32 apolloVipFlag = 42003;
  optional int32 pstnC2cVip = 42005;
  optional int32 pstnMultiVip = 42006;
  optional int32 pstnEverC2cVip = 42007;
  optional int32 pstnEverMultiVip = 42008;
  optional int32 pstnMultiTryFlag = 42011;
  optional int32 pstnC2cTryFlag = 42012;
  optional int32 subscribeNearbyassistantSwitch = 42024;
  optional int32 torchbearerFlag = 42051;
  optional int32 preloadDisableFlag = 42073;
  optional int32 reqMedalwallFlag = 42075;
  optional int32 notifyOnLikeRankingListFlag = 42092;
  optional int32 apolloStatus = 42980;
}

