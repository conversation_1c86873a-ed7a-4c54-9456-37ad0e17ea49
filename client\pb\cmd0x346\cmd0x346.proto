syntax = "proto3";

option go_package = "github.com/Mrs4s/MiraiGo/client/pb/cmd0x346";

message ApplyCleanTrafficRsp {
  int32 retCode = 10;
  string retMsg = 20;
}
message ApplyCopyFromReq {
  int64 srcUin = 10;
  int64 srcGroup = 20;
  int32 srcSvcid = 30;
  bytes srcParentfolder = 40;
  bytes srcUuid = 50;
  bytes fileMd5 = 60;
  int64 dstUin = 70;
  int64 fileSize = 80;
  string fileName = 90;
  int32 dangerLevel = 100;
  int64 totalSpace = 110;
}
message ApplyCopyFromRsp {
  int32 retCode = 10;
  string retMsg = 20;
  bytes uuid = 30;
  int64 totalSpace = 40;
}
message ApplyCopyToReq {
  int64 dstId = 10;
  int64 dstUin = 20;
  int32 dstSvcid = 30;
  int64 srcUin = 40;
  int64 fileSize = 50;
  string fileName = 60;
  string localFilepath = 70;
  bytes uuid = 80;
}
message ApplyCopyToRsp {
  int32 retCode = 10;
  string retMsg = 20;
  string fileKey = 30;
}
message ApplyDownloadAbsReq {
  int64 uin = 10;
  bytes uuid = 20;
}
message ApplyDownloadAbsRsp {
  int32 retCode = 10;
  string retMsg = 20;
  DownloadInfo downloadInfo = 30;
}
message ApplyDownloadReq {
  int64 uin = 10;
  bytes uuid = 20;
  int32 ownerType = 30;
  int32 extIntype = 500;
}
message ApplyDownloadRsp {
  int32 retCode = 10;
  string retMsg = 20;
  DownloadInfo downloadInfo = 30;
  FileInfo fileInfo = 40;
}
message ApplyForwardFileReq {
  int64 senderUin = 10;
  int64 recverUin = 20;
  bytes uuid = 30;
  int32 dangerLevel = 40;
  int64 totalSpace = 50;
}
message ApplyForwardFileRsp {
  int32 retCode = 10;
  string retMsg = 20;
  int64 totalSpace = 30;
  int64 usedSpace = 40;
  bytes uuid = 50;
}
message ApplyGetTrafficReq {
}
message ApplyGetTrafficRsp {
  int32 retCode = 10;
  string retMsg = 20;
  int64 useFileSize = 30;
  int32 useFileNum = 40;
  int64 allFileSize = 50;
  int32 allFileNum = 60;
}
message ApplyListDownloadReq {
  int64 uin = 10;
  int32 beginIndex = 20;
  int32 reqCount = 30;
}
message ApplyListDownloadRsp {
  int32 retCode = 10;
  string retMsg = 20;
  int32 totalCount = 30;
  int32 beginIndex = 40;
  int32 rspCount = 50;
  int32 isEnd = 60;
  repeated FileInfo fileList = 70;
}
message ApplyUploadHitReq {
  int64 senderUin = 10;
  int64 recverUin = 20;
  int64 fileSize = 30;
  string fileName = 40;
  bytes bytes_10mMd5 = 50;
  string localFilepath = 60;
  int32 dangerLevel = 70;
  int64 totalSpace = 80;
}
message ApplyUploadHitReqV2 {
  int64 senderUin = 10;
  int64 recverUin = 20;
  int64 fileSize = 30;
  string fileName = 40;
  bytes bytes_10mMd5 = 50;
  bytes bytes_3sha = 60;
  bytes sha = 70;
  string localFilepath = 80;
  int32 dangerLevel = 90;
  int64 totalSpace = 100;
}
message ApplyUploadHitReqV3 {
  int64 senderUin = 10;
  int64 recverUin = 20;
  int64 fileSize = 30;
  string fileName = 40;
  bytes bytes_10mMd5 = 50;
  bytes sha = 60;
  string localFilepath = 70;
  int32 dangerLevel = 80;
  int64 totalSpace = 90;
}
message ApplyUploadHitRsp {
  int32 retCode = 10;
  string retMsg = 20;
  string uploadIp = 30;
  int32 uploadPort = 40;
  string uploadDomain = 50;
  bytes uuid = 60;
  bytes uploadKey = 70;
  int64 totalSpace = 80;
  int64 usedSpace = 90;
}
message ApplyUploadHitRspV2 {
  int32 retCode = 10;
  string retMsg = 20;
  string uploadIp = 30;
  int32 uploadPort = 40;
  string uploadDomain = 50;
  bytes uuid = 60;
  bytes uploadKey = 70;
  int64 totalSpace = 80;
  int64 usedSpace = 90;
}
message ApplyUploadHitRspV3 {
  int32 retCode = 10;
  string retMsg = 20;
  string uploadIp = 30;
  int32 uploadPort = 40;
  string uploadDomain = 50;
  bytes uuid = 60;
  bytes uploadKey = 70;
  int64 totalSpace = 80;
  int64 usedSpace = 90;
}
message ApplyUploadReq {
  int64 senderUin = 10;
  int64 recverUin = 20;
  int32 fileType = 30;
  int64 fileSize = 40;
  string fileName = 50;
  bytes bytes_10mMd5 = 60;
  string localFilepath = 70;
  int32 dangerLevel = 80;
  int64 totalSpace = 90;
}
message ApplyUploadReqV2 {
  int64 senderUin = 10;
  int64 recverUin = 20;
  int64 fileSize = 30;
  string fileName = 40;
  bytes bytes_10mMd5 = 50;
  bytes bytes_3sha = 60;
  string localFilepath = 70;
  int32 dangerLevel = 80;
  int64 totalSpace = 90;
}
message ApplyUploadReqV3 {
  int64 senderUin = 10;
  int64 recverUin = 20;
  int64 fileSize = 30;
  string fileName = 40;
  bytes bytes_10mMd5 = 50;
  bytes sha = 60;
  string localFilepath = 70;
  int32 dangerLevel = 80;
  int64 totalSpace = 90;
  bytes md5 = 110;
  bytes _3Sha = 120;
}
message ApplyUploadRsp {
  int32 retCode = 10;
  string retMsg = 20;
  int64 totalSpace = 30;
  int64 usedSpace = 40;
  int64 uploadedSize = 50;
  string uploadIp = 60;
  string uploadDomain = 70;
  int32 uploadPort = 80;
  bytes uuid = 90;
  bytes uploadKey = 100;
  bool boolFileExist = 110;
  int32 packSize = 120;
  repeated string uploadipList = 130;
}
message ApplyUploadRspV2 {
  int32 retCode = 10;
  string retMsg = 20;
  int64 totalSpace = 30;
  int64 usedSpace = 40;
  int64 uploadedSize = 50;
  string uploadIp = 60;
  string uploadDomain = 70;
  int32 uploadPort = 80;
  bytes uuid = 90;
  bytes uploadKey = 100;
  bool boolFileExist = 110;
  int32 packSize = 120;
  repeated string uploadipList = 130;
  int32 httpsvrApiVer = 140;
  bytes sha = 141;
}
message ApplyUploadRspV3 {
  int32 retCode = 10;
  string retMsg = 20;
  int64 totalSpace = 30;
  int64 usedSpace = 40;
  int64 uploadedSize = 50;
  string uploadIp = 60;
  string uploadDomain = 70;
  int32 uploadPort = 80;
  bytes uuid = 90;
  bytes uploadKey = 100;
  bool boolFileExist = 110;
  int32 packSize = 120;
  repeated string uploadIpList = 130;
  int32 uploadHttpsPort = 140;
  string uploadHttpsDomain = 150;
  string uploadDns = 160;
  string uploadLanip = 170;
  bytes mediaPlateformUploadKey = 220;
}
message DelMessageReq {
  int64 uinSender = 1;
  int64 uinReceiver = 2;
  int32 time = 10;
  int32 random = 20;
  int32 seqNo = 30;
}
message DeleteFileReq {
  int64 uin = 10;
  int64 peerUin = 20;
  int32 deleteType = 30;
  bytes uuid = 40;
}
message DeleteFileRsp {
  int32 retCode = 10;
  string retMsg = 20;
}
message DownloadInfo {
  bytes downloadKey = 10;
  string downloadIp = 20;
  string downloadDomain = 30;
  int32 port = 40;
  string downloadUrl = 50;
  repeated string downloadipList = 60;
  string cookie = 70;
}
message DownloadSuccReq {
  int64 uin = 10;
  bytes uuid = 20;
}
message DownloadSuccRsp {
  int32 retCode = 10;
  string retMsg = 20;
  int32 downStat = 30;
}
message ExtensionReq {
  int64 id = 1;
  int64 type = 2;
  string dstPhonenum = 3;
  int32 phoneConvertType = 4;
  bytes sig = 20;
  int64 routeId = 100;
  DelMessageReq delMessageReq = 90100;
  int32 downloadUrlType = 90200;
  int32 pttFormat = 90300;
  int32 isNeedInnerIp = 90400;
  int32 netType = 90500;
  int32 voiceType = 90600;
  int32 fileType = 90700;
  int32 pttTime = 90800;
}
message ExtensionRsp {
}
message FileInfo {
  int64 uin = 1;
  int32 dangerEvel = 2;
  int64 fileSize = 3;
  int32 lifeTime = 4;
  int32 uploadTime = 5;
  bytes uuid = 6;
  string fileName = 7;
  int32 absFileType = 90;
  bytes bytes_10mMd5 = 100;
  bytes sha = 101;
  int32 clientType = 110;
  int64 ownerUin = 120;
  int64 peerUin = 121;
  int32 expireTime = 130;
}
message FileQueryReq {
  int64 uin = 10;
  bytes uuid = 20;
}
message FileQueryRsp {
  int32 retCode = 10;
  string retMsg = 20;
  FileInfo fileInfo = 30;
}
message RecallFileReq {
  int64 uin = 1;
  bytes uuid = 2;
}
message RecallFileRsp {
  int32 retCode = 1;
  string retMsg = 2;
}
message RecvListQueryReq {
  int64 uin = 1;
  int32 beginIndex = 2;
  int32 reqCount = 3;
}
message RecvListQueryRsp {
  int32 retCode = 1;
  string retMsg = 2;
  int32 fileTotCount = 3;
  int32 beginIndex = 4;
  int32 rspFileCount = 5;
  int32 isEnd = 6;
  repeated FileInfo fileList = 7;
}
message RenewFileReq {
  int64 uin = 1;
  bytes uuid = 2;
  int32 addTtl = 3;
}
message RenewFileRsp {
  int32 retCode = 1;
  string retMsg = 2;
}
message C346ReqBody {
  int32 cmd = 1;
  int32 seq = 2;
  /*
  RecvListQueryReq recvListQueryReq = 3;
  SendListQueryReq sendListQueryReq = 4;
  RenewFileReq renewFileReq = 5;
  RecallFileReq recallFileReq = 6;
  */
  ApplyUploadReq applyUploadReq = 7;
  // ApplyUploadHitReq applyUploadHitReq = 8;
  // ApplyForwardFileReq applyForwardFileReq = 9;
  UploadSuccReq uploadSuccReq = 10;
  // DeleteFileReq deleteFileReq = 11;
  // DownloadSuccReq downloadSuccReq = 12;
  // ApplyDownloadAbsReq applyDownloadAbsReq = 13;
  ApplyDownloadReq applyDownloadReq = 14;
  // ApplyListDownloadReq applyListDownloadReq = 15;
  // FileQueryReq fileQueryReq = 16;
  // ApplyCopyFromReq applyCopyFromReq = 17;
  // ApplyUploadReqV2 applyUploadReqV2 = 18;
  ApplyUploadReqV3 applyUploadReqV3 = 19;
  //ApplyUploadHitReqV2 applyUploadHitReqV2 = 20;
  //ApplyUploadHitReqV3 applyUploadHitReqV3 = 21;
  int32 businessId = 101;
  int32 clientType = 102;
  uint32 flagSupportMediaplatform = 200;
  //ApplyCopyToReq applyCopyToReq = 90000;
  //ApplyCleanTrafficReq applyCleanTrafficReq = 90001; empty message
  //ApplyGetTrafficReq applyGetTrafficReq = 90002;
  ExtensionReq extensionReq = 99999;
}
message C346RspBody {
  int32 cmd = 1;
  int32 seq = 2;
  /*
  RecvListQueryRsp recvListQueryRsp = 3;
  SendListQueryRsp sendListQueryRsp = 4;
  RenewFileRsp renewFileRsp = 5;
  RecallFileRsp recallFileRsp = 6;
  */
  ApplyUploadRsp applyUploadRsp = 7;
  /*
  ApplyUploadHitRsp applyUploadHitRsp = 8;
  ApplyForwardFileRsp applyForwardFileRsp = 9;
  UploadSuccRsp uploadSuccRsp = 10;
  DeleteFileRsp deleteFileRsp = 11;
  DownloadSuccRsp downloadSuccRsp = 12;
  ApplyDownloadAbsRsp applyDownloadAbsRsp = 13;
  */
  ApplyDownloadRsp applyDownloadRsp = 14;
  // ApplyListDownloadRsp applyListDownloadRsp = 15;
  // FileQueryRsp fileQueryRsp = 16;
  // ApplyCopyFromRsp applyCopyFromRsp = 17;
  // ApplyUploadRspV2 applyUploadRspV2 = 18;
  ApplyUploadRspV3 applyUploadRspV3 = 19;
  // ApplyUploadHitRspV2 applyUploadHitRspV2 = 20;
  // ApplyUploadHitRspV3 applyUploadHitRspV3 = 21;
  int32 businessId = 101;
  int32 clientType = 102;
  /*
  ApplyCopyToRsp applyCopyToRsp = 90000;
  ApplyCleanTrafficRsp applyCleanTrafficRsp = 90001;
  ApplyGetTrafficRsp applyGetTrafficRsp = 90002;
  ExtensionRsp extensionRsp = 99999;
   */
}
message SendListQueryReq {
  int64 uin = 1;
  int32 beginIndex = 2;
  int32 reqCount = 3;
}
message SendListQueryRsp {
  int32 retCode = 1;
  string retMsg = 2;
  int32 fileTotCount = 3;
  int32 beginIndex = 4;
  int32 rspFileCount = 5;
  int32 isEnd = 6;
  int64 totLimit = 7;
  int64 usedLimit = 8;
  repeated FileInfo fileList = 9;
}
message UploadSuccReq {
  int64 senderUin = 10;
  int64 recverUin = 20;
  bytes uuid = 30;
}
message UploadSuccRsp {
  int32 retCode = 10;
  string retMsg = 20;
  FileInfo fileInfo = 30;
}
