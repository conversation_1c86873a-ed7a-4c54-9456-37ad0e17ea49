# QQ机器人 - Python关键词回复实现

基于MiraiGo协议的Python QQ机器人，支持智能关键词匹配回复功能。

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Windows/Linux/macOS

### 1. 下载go-cqhttp
```bash
# 访问 https://github.com/Mrs4s/go-cqhttp/releases
# 下载对应系统版本，如：go-cqhttp_windows_amd64.exe
```

### 2. 配置go-cqhttp
创建 `config.yml`：
```yaml
account:
  uin: 你的QQ号
  password: '你的QQ密码'

servers:
  - http:
      host: 127.0.0.1
      port: 5700
      post:
        - url: 'http://127.0.0.1:8080/'
```

### 3. 安装Python依赖
```bash
# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 4. 配置机器人
编辑 `.env` 文件：
```env
SUPERUSERS=["你的QQ号"]  # 管理员QQ号
```

### 5. 启动
```bash
# 1. 先启动go-cqhttp
./go-cqhttp.exe  # Windows
./go-cqhttp       # Linux/Mac

# 2. 再启动Python机器人
python bot.py
# 或使用启动脚本
start.bat    # Windows
./start.sh   # Linux/Mac
```

## 📋 功能特性

### ✨ 关键词匹配类型

| 类型 | 说明 | 示例 |
|------|------|------|
| **精确匹配** | 完全匹配关键词 | 用户："你好" → 机器人："你好！" |
| **模糊匹配** | 包含关键词即可 | 用户："今天天气怎么样" → 机器人："今天天气不错呢！" |
| **正则匹配** | 正则表达式匹配 | 用户："1+1" → 机器人："计算结果是: 2" |

### 🎯 默认关键词

#### 精确匹配
- `你好` → "你好！" / "Hi！" / "欢迎！"
- `再见` → "再见！" / "拜拜！" / "下次见！"
- `谢谢` → "不客气！" / "不用谢！" / "很高兴帮到你！"

#### 模糊匹配
- 包含`天气` → "今天天气不错呢！" / "记得关注天气变化哦！"
- 包含`学习` → "学习使人进步！" / "加油学习！" / "知识就是力量！"
- 包含`吃饭` → "记得按时吃饭！" / "要好好吃饭哦！"

#### 正则匹配
- `数字+数字` → 自动计算加法
- `时间|几点` → 返回当前时间

### 🛠️ 管理命令

仅管理员可用的命令：

```bash
# 添加关键词
/添加关键词 exact 新关键词 回复1 回复2 回复3

# 删除关键词
/删除关键词 exact 关键词

# 查看所有关键词
/关键词列表
```

#### 示例
```bash
# 添加精确匹配
/添加关键词 exact 早安 早上好！ 新的一天开始了！

# 添加模糊匹配
/添加关键词 fuzzy 编程 编程改变世界！ 代码就是艺术！

# 添加正则匹配
/添加关键词 regex \d+岁 你今年{age}岁啊！
```

## 📁 项目结构

```
qq_bot/
├── bot.py                 # 主程序入口
├── .env                   # 环境配置
├── requirements.txt       # Python依赖
├── start.bat             # Windows启动脚本
├── start.sh              # Linux/Mac启动脚本
├── setup_guide.md        # 详细安装指南
├── plugins/              # 插件目录
│   ├── __init__.py
│   └── keyword_reply.py  # 关键词回复插件
└── data/
    └── keywords.json     # 关键词数据文件
```

## 🔧 自定义开发

### 添加新的匹配规则
编辑 `plugins/keyword_reply.py` 中的 `match_message` 方法：

```python
def match_message(self, message: str) -> str:
    # 添加你的自定义匹配逻辑
    if "自定义关键词" in message:
        return "自定义回复"
    
    # 原有匹配逻辑...
```

### 创建新插件
在 `plugins/` 目录下创建新的 `.py` 文件，NoneBot2会自动加载。

## ❓ 常见问题

### Q: 机器人没有回复消息？
A: 检查以下几点：
1. go-cqhttp是否正常运行并登录成功
2. Python机器人是否正常启动
3. 端口配置是否正确（5700和8080）
4. 查看控制台是否有错误信息

### Q: 管理命令无效？
A: 确认：
1. `.env` 文件中的 `SUPERUSERS` 配置了正确的QQ号
2. 命令格式是否正确
3. 是否有足够的权限

### Q: 如何修改端口？
A: 
1. 修改 `.env` 文件中的 `PORT`
2. 修改 `config.yml` 中对应的端口配置

### Q: 支持群聊和私聊吗？
A: 是的，机器人同时支持群聊和私聊消息。

## 📝 更新日志

### v1.0.0
- ✅ 基础关键词匹配功能
- ✅ 精确/模糊/正则三种匹配模式
- ✅ 管理员命令系统
- ✅ 数据持久化存储
- ✅ 群聊和私聊支持

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## ⚠️ 免责声明

本项目仅供学习交流使用，请遵守相关法律法规和平台规则。
