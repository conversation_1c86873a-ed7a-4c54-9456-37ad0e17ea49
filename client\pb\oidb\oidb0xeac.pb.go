// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0xeac.proto

package oidb

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type EACReqBody struct {
	GroupCode proto.Option[uint64] `protobuf:"varint,1,opt"`
	Seq       proto.Option[uint32] `protobuf:"varint,2,opt"`
	Random    proto.Option[uint32] `protobuf:"varint,3,opt"`
	_         [0]func()
}

type EACRspBody struct {
	Wording    proto.Option[string] `protobuf:"bytes,1,opt"`
	DigestUin  proto.Option[uint64] `protobuf:"varint,2,opt"`
	DigestTime proto.Option[uint32] `protobuf:"varint,3,opt"`
	// optional DigestMsg msg = 4;
	ErrorCode proto.Option[uint32] `protobuf:"varint,10,opt"`
	_         [0]func()
}
