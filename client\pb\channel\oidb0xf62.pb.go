// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/channel/oidb0xf62.proto

package channel

import (
	msg "github.com/Mrs4s/MiraiGo/client/pb/msg"
	proto "github.com/RomiChan/protobuf/proto"
)

type DF62ReqBody struct {
	Msg *ChannelMsgContent `protobuf:"bytes,1,opt"`
	_   [0]func()
}

type DF62RspBody struct {
	Result        proto.Option[uint32]  `protobuf:"varint,1,opt"`
	Errmsg        []byte                `protobuf:"bytes,2,opt"`
	SendTime      proto.Option[uint32]  `protobuf:"varint,3,opt"`
	Head          *ChannelMsgHead       `protobuf:"bytes,4,opt"`
	ErrType       proto.Option[uint32]  `protobuf:"varint,5,opt"`
	TransSvrInfo  *TransSvrInfo         `protobuf:"bytes,6,opt"`
	FreqLimitInfo *ChannelFreqLimitInfo `protobuf:"bytes,7,opt"`
	Body          *msg.MessageBody      `protobuf:"bytes,8,opt"`
}

type TransSvrInfo struct {
	SubType   proto.Option[uint32] `protobuf:"varint,1,opt"`
	RetCode   proto.Option[int32]  `protobuf:"varint,2,opt"`
	ErrMsg    []byte               `protobuf:"bytes,3,opt"`
	TransInfo []byte               `protobuf:"bytes,4,opt"`
}
