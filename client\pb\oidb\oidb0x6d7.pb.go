// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0x6d7.proto

package oidb

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type CreateFolderReqBody struct {
	GroupCode      proto.Option[uint64] `protobuf:"varint,1,opt"`
	AppId          proto.Option[uint32] `protobuf:"varint,2,opt"`
	ParentFolderId proto.Option[string] `protobuf:"bytes,3,opt"`
	FolderName     proto.Option[string] `protobuf:"bytes,4,opt"`
	_              [0]func()
}

type CreateFolderRspBody struct {
	RetCode       proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording proto.Option[string] `protobuf:"bytes,3,opt"` // optional group_file_common.FolderInfo folderInfo = 4;
	_             [0]func()
}

type DeleteFolderReqBody struct {
	GroupCode proto.Option[uint64] `protobuf:"varint,1,opt"`
	AppId     proto.Option[uint32] `protobuf:"varint,2,opt"`
	FolderId  proto.Option[string] `protobuf:"bytes,3,opt"`
	_         [0]func()
}

type DeleteFolderRspBody struct {
	RetCode       proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording proto.Option[string] `protobuf:"bytes,3,opt"`
	_             [0]func()
}

type MoveFolderReqBody struct {
	GroupCode      proto.Option[uint64] `protobuf:"varint,1,opt"`
	AppId          proto.Option[uint32] `protobuf:"varint,2,opt"`
	FolderId       proto.Option[string] `protobuf:"bytes,3,opt"`
	ParentFolderId proto.Option[string] `protobuf:"bytes,4,opt"`
	DestFolderId   proto.Option[string] `protobuf:"bytes,5,opt"`
	_              [0]func()
}

type MoveFolderRspBody struct {
	RetCode       proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording proto.Option[string] `protobuf:"bytes,3,opt"` // optional group_file_common.FolderInfo folderInfo = 4;
	_             [0]func()
}

type RenameFolderReqBody struct {
	GroupCode     proto.Option[uint64] `protobuf:"varint,1,opt"`
	AppId         proto.Option[uint32] `protobuf:"varint,2,opt"`
	FolderId      proto.Option[string] `protobuf:"bytes,3,opt"`
	NewFolderName proto.Option[string] `protobuf:"bytes,4,opt"`
	_             [0]func()
}

type RenameFolderRspBody struct {
	RetCode       proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording proto.Option[string] `protobuf:"bytes,3,opt"` // optional group_file_common.FolderInfo folderInfo = 4;
	_             [0]func()
}

type D6D7ReqBody struct {
	CreateFolderReq *CreateFolderReqBody `protobuf:"bytes,1,opt"`
	DeleteFolderReq *DeleteFolderReqBody `protobuf:"bytes,2,opt"`
	RenameFolderReq *RenameFolderReqBody `protobuf:"bytes,3,opt"` // optional MoveFolderReqBody moveFolderReq = 4;
	_               [0]func()
}

type D6D7RspBody struct {
	CreateFolderRsp *CreateFolderRspBody `protobuf:"bytes,1,opt"`
	DeleteFolderRsp *DeleteFolderRspBody `protobuf:"bytes,2,opt"`
	RenameFolderRsp *RenameFolderRspBody `protobuf:"bytes,3,opt"` // optional MoveFolderRspBody moveFolderRsp = 4;
	_               [0]func()
}
