// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/notify/group0x857.proto

package notify

type NotifyMsgBody struct {
	OptMsgGrayTips    *AIOGrayTipsInfo       `protobuf:"bytes,5,opt"`
	OptMsgRedTips     *RedGrayTipsInfo       `protobuf:"bytes,9,opt"`
	OptMsgRecall      *MessageRecallReminder `protobuf:"bytes,11,opt"`
	OptGeneralGrayTip *GeneralGrayTipInfo    `protobuf:"bytes,26,opt"`
	QqGroupDigestMsg  *QQGroupDigestMsg      `protobuf:"bytes,33,opt"`
	ServiceType       int32                  `protobuf:"varint,13,opt"`
	_                 [0]func()
}

type AIOGrayTipsInfo struct {
	ShowLatest     uint32 `protobuf:"varint,1,opt"`
	Content        []byte `protobuf:"bytes,2,opt"`
	Remind         uint32 `protobuf:"varint,3,opt"`
	Brief          []byte `protobuf:"bytes,4,opt"`
	ReceiverUin    uint64 `protobuf:"varint,5,opt"`
	ReliaoAdminOpt uint32 `protobuf:"varint,6,opt"`
}

type GeneralGrayTipInfo struct {
	BusiType      uint64        `protobuf:"varint,1,opt"`
	BusiId        uint64        `protobuf:"varint,2,opt"`
	CtrlFlag      uint32        `protobuf:"varint,3,opt"`
	C2CType       uint32        `protobuf:"varint,4,opt"`
	ServiceType   uint32        `protobuf:"varint,5,opt"`
	TemplId       uint64        `protobuf:"varint,6,opt"`
	MsgTemplParam []*TemplParam `protobuf:"bytes,7,rep"`
	Content       string        `protobuf:"bytes,8,opt"`
}

type TemplParam struct {
	Name  string `protobuf:"bytes,1,opt"`
	Value string `protobuf:"bytes,2,opt"`
	_     [0]func()
}

type MessageRecallReminder struct {
	Uin             int64                  `protobuf:"varint,1,opt"`
	Nickname        []byte                 `protobuf:"bytes,2,opt"`
	RecalledMsgList []*RecalledMessageMeta `protobuf:"bytes,3,rep"`
	ReminderContent []byte                 `protobuf:"bytes,4,opt"`
	Userdef         []byte                 `protobuf:"bytes,5,opt"`
	GroupType       int32                  `protobuf:"varint,6,opt"`
	OpType          int32                  `protobuf:"varint,7,opt"`
}

type RecalledMessageMeta struct {
	Seq       int32 `protobuf:"varint,1,opt"`
	Time      int32 `protobuf:"varint,2,opt"`
	MsgRandom int32 `protobuf:"varint,3,opt"`
	MsgType   int32 `protobuf:"varint,4,opt"`
	MsgFlag   int32 `protobuf:"varint,5,opt"`
	AuthorUin int64 `protobuf:"varint,6,opt"`
	_         [0]func()
}

type RedGrayTipsInfo struct {
	ShowLatest          uint32 `protobuf:"varint,1,opt"`
	SenderUin           uint64 `protobuf:"varint,2,opt"`
	ReceiverUin         uint64 `protobuf:"varint,3,opt"`
	SenderRichContent   string `protobuf:"bytes,4,opt"`
	ReceiverRichContent string `protobuf:"bytes,5,opt"`
	AuthKey             []byte `protobuf:"bytes,6,opt"`
	MsgType             int32  `protobuf:"zigzag32,7,opt"`
	LuckyFlag           uint32 `protobuf:"varint,8,opt"`
	HideFlag            uint32 `protobuf:"varint,9,opt"`
	LuckyUin            uint64 `protobuf:"varint,12,opt"`
}

type QQGroupDigestMsg struct {
	GroupCode     uint64 `protobuf:"varint,1,opt"`
	Seq           uint32 `protobuf:"varint,2,opt"`
	Random        uint32 `protobuf:"varint,3,opt"`
	OpType        int32  `protobuf:"varint,4,opt"`
	Sender        uint64 `protobuf:"varint,5,opt"`
	DigestOper    uint64 `protobuf:"varint,6,opt"`
	OpTime        uint32 `protobuf:"varint,7,opt"`
	LastestMsgSeq uint32 `protobuf:"varint,8,opt"`
	OperNick      []byte `protobuf:"bytes,9,opt"`
	SenderNick    []byte `protobuf:"bytes,10,opt"`
	ExtInfo       int32  `protobuf:"varint,11,opt"`
}
