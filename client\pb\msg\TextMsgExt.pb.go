// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/msg/TextMsgExt.proto

package msg

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type ExtChannelInfo struct {
	GuildId   proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChannelId proto.Option[uint64] `protobuf:"varint,2,opt"`
	_         [0]func()
}

type TextResvAttr struct {
	Wording            []byte               `protobuf:"bytes,1,opt"`
	TextAnalysisResult proto.Option[uint32] `protobuf:"varint,2,opt"`
	AtType             proto.Option[uint32] `protobuf:"varint,3,opt"`
	AtMemberUin        proto.Option[uint64] `protobuf:"varint,4,opt"`
	AtMemberTinyid     proto.Option[uint64] `protobuf:"varint,5,opt"`
	// optional ExtRoleInfo atMemberRoleInfo = 6;
	// optional ExtRoleInfo atRoleInfo = 7;
	AtChannelInfo *ExtChannelInfo `protobuf:"bytes,8,opt"`
}

type ExtRoleInfo struct {
	Id   proto.Option[uint64] `protobuf:"varint,1,opt"`
	Info []byte               `protobuf:"bytes,2,opt"`
	Flag proto.Option[uint32] `protobuf:"varint,3,opt"`
}
