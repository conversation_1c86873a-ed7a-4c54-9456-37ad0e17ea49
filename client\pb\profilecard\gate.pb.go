// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/profilecard/gate.proto

package profilecard

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type GateCommTaskInfo struct {
	Appid    proto.Option[int32] `protobuf:"varint,1,opt"`
	TaskData []byte              `protobuf:"bytes,2,opt"`
}

type GateGetGiftListReq struct {
	Uin proto.Option[int32] `protobuf:"varint,1,opt"`
	_   [0]func()
}

type GateGetGiftListRsp struct {
	GiftUrl   []string             `protobuf:"bytes,1,rep"`
	CustomUrl proto.Option[string] `protobuf:"bytes,2,opt"`
	Desc      proto.Option[string] `protobuf:"bytes,3,opt"`
	IsOn      proto.Option[bool]   `protobuf:"varint,4,opt"`
}

type GateGetVipCareReq struct {
	Uin proto.Option[int64] `protobuf:"varint,1,opt"`
	_   [0]func()
}

type GateGetVipCareRsp struct {
	Buss   proto.Option[int32] `protobuf:"varint,1,opt"`
	Notice proto.Option[int32] `protobuf:"varint,2,opt"`
	_      [0]func()
}

type GateOidbFlagInfo struct {
	Fieled     proto.Option[int32] `protobuf:"varint,1,opt"`
	ByetsValue []byte              `protobuf:"bytes,2,opt"`
}

type GatePrivilegeBaseInfoReq struct {
	UReqUin proto.Option[int64] `protobuf:"varint,1,opt"`
	_       [0]func()
}

type GatePrivilegeBaseInfoRsp struct {
	Msg        []byte               `protobuf:"bytes,1,opt"`
	JumpUrl    []byte               `protobuf:"bytes,2,opt"`
	VOpenPriv  []*GatePrivilegeInfo `protobuf:"bytes,3,rep"`
	VClosePriv []*GatePrivilegeInfo `protobuf:"bytes,4,rep"`
	UIsGrayUsr proto.Option[int32]  `protobuf:"varint,5,opt"`
}

type GatePrivilegeInfo struct {
	IType         proto.Option[int32] `protobuf:"varint,1,opt"`
	ISort         proto.Option[int32] `protobuf:"varint,2,opt"`
	IFeeType      proto.Option[int32] `protobuf:"varint,3,opt"`
	ILevel        proto.Option[int32] `protobuf:"varint,4,opt"`
	IFlag         proto.Option[int32] `protobuf:"varint,5,opt"`
	IconUrl       []byte              `protobuf:"bytes,6,opt"`
	DeluxeIconUrl []byte              `protobuf:"bytes,7,opt"`
	JumpUrl       []byte              `protobuf:"bytes,8,opt"`
	IIsBig        proto.Option[int32] `protobuf:"varint,9,opt"`
}

type GateVaProfileGateReq struct {
	UCmd           proto.Option[int32]       `protobuf:"varint,1,opt"`
	StPrivilegeReq *GatePrivilegeBaseInfoReq `protobuf:"bytes,2,opt"`
	StGiftReq      *GateGetGiftListReq       `protobuf:"bytes,3,opt"`
	// repeated GateCommTaskInfo taskItem = 4;
	OidbFlag  []*GateOidbFlagInfo `protobuf:"bytes,5,rep"`
	StVipCare *GateGetVipCareReq  `protobuf:"bytes,6,opt"`
}

type GateQidInfoItem struct {
	Qid     proto.Option[string] `protobuf:"bytes,1,opt"`
	Url     proto.Option[string] `protobuf:"bytes,2,opt"`
	Color   proto.Option[string] `protobuf:"bytes,3,opt"`
	LogoUrl proto.Option[string] `protobuf:"bytes,4,opt"`
	_       [0]func()
}

type GateVaProfileGateRsp struct {
	IRetCode proto.Option[int32] `protobuf:"varint,1,opt"`
	SRetMsg  []byte              `protobuf:"bytes,2,opt"`
	// optional GatePrivilegeBaseInfoRsp stPrivilegeRsp = 3;
	// optional GateGetGiftListRsp stGiftRsp = 4;
	// repeated GateCommTaskInfo taskItem = 5;
	// repeated GateOidbFlagInfo oidbFlag = 6;
	// optional GateGetVipCareRsp stVipCare = 7;
	QidInfo *GateQidInfoItem `protobuf:"bytes,9,opt"`
}
