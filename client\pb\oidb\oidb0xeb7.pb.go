// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0xeb7.proto

package oidb

import (
	proto "github.com/RomiChan/protobuf/proto"
)

// DEB7 prefix
type DEB7ReqBody struct {
	// optional StSignInStatusReq signInStatusReq = 1;
	SignInWriteReq *StSignInWriteReq `protobuf:"bytes,2,opt"`
	_              [0]func()
}

type DEB7Ret struct {
	Code proto.Option[uint32] `protobuf:"varint,1,opt"`
	Msg  proto.Option[string] `protobuf:"bytes,2,opt"`
	_    [0]func()
}

type DEB7RspBody struct {
	SignInStatusRsp *StSignInStatusRsp `protobuf:"bytes,1,opt"`
	SignInWriteRsp  *StSignInWriteRsp  `protobuf:"bytes,2,opt"`
	_               [0]func()
}

type SignInStatusBase struct {
	Status           proto.Option[uint32] `protobuf:"varint,1,opt"`
	CurrentTimeStamp proto.Option[int64]  `protobuf:"varint,2,opt"`
	_                [0]func()
}

type SignInStatusDoneInfo struct {
	LeftTitleWrod      proto.Option[string] `protobuf:"bytes,1,opt"`
	RightDescWord      proto.Option[string] `protobuf:"bytes,2,opt"`
	BelowPortraitWords []string             `protobuf:"bytes,3,rep"`
	RecordUrl          proto.Option[string] `protobuf:"bytes,4,opt"`
}

type SignInStatusGroupScore struct {
	GroupScoreWord proto.Option[string] `protobuf:"bytes,1,opt"`
	ScoreUrl       proto.Option[string] `protobuf:"bytes,2,opt"`
	_              [0]func()
}

type SignInStatusNotInfo struct {
	ButtonWord        proto.Option[string] `protobuf:"bytes,1,opt"`
	SignDescWordLeft  proto.Option[string] `protobuf:"bytes,2,opt"`
	SignDescWordRight proto.Option[string] `protobuf:"bytes,3,opt"`
	_                 [0]func()
}

type SignInStatusYesterdayFirst struct {
	YesterdayFirstUid proto.Option[string] `protobuf:"bytes,1,opt"`
	YesterdayWord     proto.Option[string] `protobuf:"bytes,2,opt"`
	YesterdayNick     proto.Option[string] `protobuf:"bytes,3,opt"`
	_                 [0]func()
}

type StDaySignedInfo struct {
	Uid             proto.Option[string] `protobuf:"bytes,1,opt"`
	UidGroupNick    proto.Option[string] `protobuf:"bytes,2,opt"`
	SignedTimeStamp proto.Option[int64]  `protobuf:"varint,3,opt"`
	SignInRank      proto.Option[int32]  `protobuf:"varint,4,opt"`
	_               [0]func()
}

type StDaySignedListReq struct {
	DayYmd  proto.Option[string] `protobuf:"bytes,1,opt"`
	Uid     proto.Option[string] `protobuf:"bytes,2,opt"`
	GroupId proto.Option[string] `protobuf:"bytes,3,opt"`
	Offset  proto.Option[int32]  `protobuf:"varint,4,opt"`
	Limit   proto.Option[int32]  `protobuf:"varint,5,opt"`
	_       [0]func()
}

type StDaySignedListRsp struct {
	Ret  *DEB7Ret           `protobuf:"bytes,1,opt"`
	Page []*StDaySignedPage `protobuf:"bytes,2,rep"`
}

type StDaySignedPage struct {
	Infos  []*StDaySignedInfo  `protobuf:"bytes,1,rep"`
	Offset proto.Option[int32] `protobuf:"varint,2,opt"`
	Total  proto.Option[int32] `protobuf:"varint,3,opt"`
}

type StKingSignedInfo struct {
	Uid             proto.Option[string] `protobuf:"bytes,1,opt"`
	GroupNick       proto.Option[string] `protobuf:"bytes,2,opt"`
	SignedTimeStamp proto.Option[int64]  `protobuf:"varint,3,opt"`
	SignedCount     proto.Option[int32]  `protobuf:"varint,4,opt"`
	_               [0]func()
}

type StKingSignedListReq struct {
	Uid     proto.Option[string] `protobuf:"bytes,1,opt"`
	GroupId proto.Option[string] `protobuf:"bytes,2,opt"`
	_       [0]func()
}

type StKingSignedListRsp struct {
	Ret               *DEB7Ret            `protobuf:"bytes,1,opt"`
	YesterdayFirst    *StKingSignedInfo   `protobuf:"bytes,2,opt"`
	TopSignedTotal    []*StKingSignedInfo `protobuf:"bytes,3,rep"`
	TopSignedContinue []*StKingSignedInfo `protobuf:"bytes,4,rep"`
}

type StSignInRecordDaySigned struct {
	DaySignedRatio    proto.Option[float32] `protobuf:"fixed32,1,opt"`
	DayTotalSignedUid proto.Option[int32]   `protobuf:"varint,2,opt"`
	DaySignedPage     *StDaySignedPage      `protobuf:"bytes,3,opt"`
	DaySignedUrl      proto.Option[string]  `protobuf:"bytes,4,opt"`
	_                 [0]func()
}

type StSignInRecordKing struct {
	YesterdayFirst    *StKingSignedInfo    `protobuf:"bytes,1,opt"`
	TopSignedTotal    []*StKingSignedInfo  `protobuf:"bytes,2,rep"`
	TopSignedContinue []*StKingSignedInfo  `protobuf:"bytes,3,rep"`
	KingUrl           proto.Option[string] `protobuf:"bytes,4,opt"`
}

type StSignInRecordReq struct {
	DayYmd  proto.Option[string] `protobuf:"bytes,1,opt"`
	Uid     proto.Option[string] `protobuf:"bytes,2,opt"`
	GroupId proto.Option[string] `protobuf:"bytes,3,opt"`
	_       [0]func()
}

type StSignInRecordRsp struct {
	Ret        *DEB7Ret                 `protobuf:"bytes,1,opt"`
	Base       *SignInStatusBase        `protobuf:"bytes,2,opt"`
	UserRecord *StSignInRecordUser      `protobuf:"bytes,3,opt"`
	DaySigned  *StSignInRecordDaySigned `protobuf:"bytes,4,opt"`
	KingRecord *StSignInRecordKing      `protobuf:"bytes,5,opt"`
	Level      *StViewGroupLevel        `protobuf:"bytes,6,opt"`
	_          [0]func()
}

type StSignInRecordUser struct {
	TotalSignedDays         proto.Option[int32]  `protobuf:"varint,2,opt"`
	EarliestSignedTimeStamp proto.Option[int64]  `protobuf:"varint,3,opt"`
	ContinueSignedDays      proto.Option[int64]  `protobuf:"varint,4,opt"`
	HistorySignedDays       []string             `protobuf:"bytes,5,rep"`
	GroupName               proto.Option[string] `protobuf:"bytes,6,opt"`
}

type StSignInStatusReq struct {
	Uid           proto.Option[string] `protobuf:"bytes,1,opt"`
	GroupId       proto.Option[string] `protobuf:"bytes,2,opt"`
	Scene         proto.Option[uint32] `protobuf:"varint,3,opt"`
	ClientVersion proto.Option[string] `protobuf:"bytes,4,opt"`
	_             [0]func()
}

type StSignInStatusRsp struct {
	Ret           *DEB7Ret                    `protobuf:"bytes,1,opt"`
	Base          *SignInStatusBase           `protobuf:"bytes,2,opt"`
	Yesterday     *SignInStatusYesterdayFirst `protobuf:"bytes,3,opt"`
	NotInfo       *SignInStatusNotInfo        `protobuf:"bytes,4,opt"`
	DoneInfo      *SignInStatusDoneInfo       `protobuf:"bytes,5,opt"`
	GroupScore    *SignInStatusGroupScore     `protobuf:"bytes,6,opt"`
	MantleUrl     proto.Option[string]        `protobuf:"bytes,7,opt"`
	BackgroundUrl proto.Option[string]        `protobuf:"bytes,8,opt"`
	_             [0]func()
}

type StSignInWriteReq struct {
	Uid           proto.Option[string] `protobuf:"bytes,1,opt"`
	GroupId       proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientVersion proto.Option[string] `protobuf:"bytes,3,opt"`
	_             [0]func()
}

type StSignInWriteRsp struct {
	Ret        *DEB7Ret                `protobuf:"bytes,1,opt"`
	DoneInfo   *SignInStatusDoneInfo   `protobuf:"bytes,2,opt"`
	GroupScore *SignInStatusGroupScore `protobuf:"bytes,3,opt"`
	_          [0]func()
}

type StViewGroupLevel struct {
	Title proto.Option[string] `protobuf:"bytes,1,opt"`
	Url   proto.Option[string] `protobuf:"bytes,2,opt"`
	_     [0]func()
}
