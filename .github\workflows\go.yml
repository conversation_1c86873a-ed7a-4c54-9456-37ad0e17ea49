name: Go

on:
  push:
    branches: [ master, typeparam ]
  pull_request:
    branches: [ master, typeparam ]

jobs:

  build:
    name: Build
    runs-on: ubuntu-latest
    steps:

    - name: Set up Go 1.x
      uses: actions/setup-go@v2
      with:
        go-version: '1.20'

    - name: Check out code into the Go module directory
      uses: actions/checkout@v2

    - name: Get dependencies
      run: |
        go get -v -t -d ./...

    - name: Vet
      run: go vet -stdmethods=false ./...

    - name: Build
      run: go build -v ./...

    - name: Test
      run: go test -v ./...
