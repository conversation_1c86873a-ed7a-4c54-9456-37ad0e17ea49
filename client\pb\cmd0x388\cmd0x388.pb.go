// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/cmd0x388/cmd0x388.proto

package cmd0x388

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type DelImgReq struct {
	SrcUin          proto.Option[uint64] `protobuf:"varint,1,opt"`
	DstUin          proto.Option[uint64] `protobuf:"varint,2,opt"`
	ReqTerm         proto.Option[uint32] `protobuf:"varint,3,opt"`
	ReqPlatformType proto.Option[uint32] `protobuf:"varint,4,opt"`
	BuType          proto.Option[uint32] `protobuf:"varint,5,opt"`
	BuildVer        []byte               `protobuf:"bytes,6,opt"`
	FileResid       []byte               `protobuf:"bytes,7,opt"`
	PicWidth        proto.Option[uint32] `protobuf:"varint,8,opt"`
	PicHeight       proto.Option[uint32] `protobuf:"varint,9,opt"`
}

type DelImgRsp struct {
	Result    proto.Option[uint32] `protobuf:"varint,1,opt"`
	FailMsg   []byte               `protobuf:"bytes,2,opt"`
	FileResid []byte               `protobuf:"bytes,3,opt"`
}

type ExpRoamExtendInfo struct {
	Resid []byte `protobuf:"bytes,1,opt"`
}

type ExpRoamPicInfo struct {
	ShopFlag proto.Option[uint32] `protobuf:"varint,1,opt"`
	PkgId    proto.Option[uint32] `protobuf:"varint,2,opt"`
	PicId    []byte               `protobuf:"bytes,3,opt"`
}

type ExtensionCommPicTryUp struct {
	Extinfo [][]byte `protobuf:"bytes,1,rep"`
}

type ExtensionExpRoamTryUp struct {
	ExproamPicInfo []*ExpRoamPicInfo `protobuf:"bytes,1,rep"`
}

type GetImgUrlReq struct {
	GroupCode       proto.Option[uint64] `protobuf:"varint,1,opt"`
	DstUin          proto.Option[uint64] `protobuf:"varint,2,opt"`
	Fileid          proto.Option[uint64] `protobuf:"varint,3,opt"`
	FileMd5         []byte               `protobuf:"bytes,4,opt"`
	UrlFlag         proto.Option[uint32] `protobuf:"varint,5,opt"`
	UrlType         proto.Option[uint32] `protobuf:"varint,6,opt"`
	ReqTerm         proto.Option[uint32] `protobuf:"varint,7,opt"`
	ReqPlatformType proto.Option[uint32] `protobuf:"varint,8,opt"`
	InnerIp         proto.Option[uint32] `protobuf:"varint,9,opt"`
	BuType          proto.Option[uint32] `protobuf:"varint,10,opt"`
	BuildVer        []byte               `protobuf:"bytes,11,opt"`
	FileId          proto.Option[uint64] `protobuf:"varint,12,opt"`
	FileSize        proto.Option[uint64] `protobuf:"varint,13,opt"`
	OriginalPic     proto.Option[uint32] `protobuf:"varint,14,opt"`
	RetryReq        proto.Option[uint32] `protobuf:"varint,15,opt"`
	FileHeight      proto.Option[uint32] `protobuf:"varint,16,opt"`
	FileWidth       proto.Option[uint32] `protobuf:"varint,17,opt"`
	PicType         proto.Option[uint32] `protobuf:"varint,18,opt"`
	PicUpTimestamp  proto.Option[uint32] `protobuf:"varint,19,opt"`
	ReqTransferType proto.Option[uint32] `protobuf:"varint,20,opt"`
	QqmeetGuildId   proto.Option[uint64] `protobuf:"varint,21,opt"`
	QqmeetChannelId proto.Option[uint64] `protobuf:"varint,22,opt"`
	DownloadIndex   []byte               `protobuf:"bytes,23,opt"`
}

type GetImgUrlRsp struct {
	Fileid           proto.Option[uint64] `protobuf:"varint,1,opt"`
	FileMd5          []byte               `protobuf:"bytes,2,opt"`
	Result           proto.Option[uint32] `protobuf:"varint,3,opt"`
	FailMsg          []byte               `protobuf:"bytes,4,opt"`
	ImgInfo          *ImgInfo             `protobuf:"bytes,5,opt"`
	ThumbDownUrl     [][]byte             `protobuf:"bytes,6,rep"`
	OriginalDownUrl  [][]byte             `protobuf:"bytes,7,rep"`
	BigDownUrl       [][]byte             `protobuf:"bytes,8,rep"`
	DownIp           []uint32             `protobuf:"varint,9,rep"`
	DownPort         []uint32             `protobuf:"varint,10,rep"`
	DownDomain       []byte               `protobuf:"bytes,11,opt"`
	ThumbDownPara    []byte               `protobuf:"bytes,12,opt"`
	OriginalDownPara []byte               `protobuf:"bytes,13,opt"`
	BigDownPara      []byte               `protobuf:"bytes,14,opt"`
	FileId           proto.Option[uint64] `protobuf:"varint,15,opt"`
	AutoDownType     proto.Option[uint32] `protobuf:"varint,16,opt"`
	OrderDownType    []uint32             `protobuf:"varint,17,rep"`
	BigThumbDownPara []byte               `protobuf:"bytes,19,opt"`
	HttpsUrlFlag     proto.Option[uint32] `protobuf:"varint,20,opt"`
	DownIp6          []*IPv6Info          `protobuf:"bytes,26,rep"`
	ClientIp6        []byte               `protobuf:"bytes,27,opt"`
}

type GetPttUrlReq struct {
	GroupCode       proto.Option[uint64] `protobuf:"varint,1,opt"`
	DstUin          proto.Option[uint64] `protobuf:"varint,2,opt"`
	Fileid          proto.Option[uint64] `protobuf:"varint,3,opt"`
	FileMd5         []byte               `protobuf:"bytes,4,opt"`
	ReqTerm         proto.Option[uint32] `protobuf:"varint,5,opt"`
	ReqPlatformType proto.Option[uint32] `protobuf:"varint,6,opt"`
	InnerIp         proto.Option[uint32] `protobuf:"varint,7,opt"`
	BuType          proto.Option[uint32] `protobuf:"varint,8,opt"`
	BuildVer        []byte               `protobuf:"bytes,9,opt"`
	FileId          proto.Option[uint64] `protobuf:"varint,10,opt"`
	FileKey         []byte               `protobuf:"bytes,11,opt"`
	Codec           proto.Option[uint32] `protobuf:"varint,12,opt"`
	BuId            proto.Option[uint32] `protobuf:"varint,13,opt"`
	ReqTransferType proto.Option[uint32] `protobuf:"varint,14,opt"`
	IsAuto          proto.Option[uint32] `protobuf:"varint,15,opt"`
}

type GetPttUrlRsp struct {
	Fileid       proto.Option[uint64] `protobuf:"varint,1,opt"`
	FileMd5      []byte               `protobuf:"bytes,2,opt"`
	Result       proto.Option[uint32] `protobuf:"varint,3,opt"`
	FailMsg      []byte               `protobuf:"bytes,4,opt"`
	DownUrl      [][]byte             `protobuf:"bytes,5,rep"`
	DownIp       []uint32             `protobuf:"varint,6,rep"`
	DownPort     []uint32             `protobuf:"varint,7,rep"`
	DownDomain   []byte               `protobuf:"bytes,8,opt"`
	DownPara     []byte               `protobuf:"bytes,9,opt"`
	FileId       proto.Option[uint64] `protobuf:"varint,10,opt"`
	TransferType proto.Option[uint32] `protobuf:"varint,11,opt"`
	AllowRetry   proto.Option[uint32] `protobuf:"varint,12,opt"`
	DownIp6      []*IPv6Info          `protobuf:"bytes,26,rep"`
	ClientIp6    []byte               `protobuf:"bytes,27,opt"`
	Domain       proto.Option[string] `protobuf:"bytes,28,opt"`
}

type IPv6Info struct {
	Ip6  []byte               `protobuf:"bytes,1,opt"`
	Port proto.Option[uint32] `protobuf:"varint,2,opt"`
}

type ImgInfo struct {
	FileMd5    []byte               `protobuf:"bytes,1,opt"`
	FileType   proto.Option[uint32] `protobuf:"varint,2,opt"`
	FileSize   proto.Option[uint64] `protobuf:"varint,3,opt"`
	FileWidth  proto.Option[uint32] `protobuf:"varint,4,opt"`
	FileHeight proto.Option[uint32] `protobuf:"varint,5,opt"`
}

type PicSize struct {
	Original proto.Option[uint32] `protobuf:"varint,1,opt"`
	Thumb    proto.Option[uint32] `protobuf:"varint,2,opt"`
	High     proto.Option[uint32] `protobuf:"varint,3,opt"`
	_        [0]func()
}

type D388ReqBody struct {
	NetType      proto.Option[uint32] `protobuf:"varint,1,opt"`
	Subcmd       proto.Option[uint32] `protobuf:"varint,2,opt"`
	TryupImgReq  []*TryUpImgReq       `protobuf:"bytes,3,rep"`
	GetimgUrlReq []*GetImgUrlReq      `protobuf:"bytes,4,rep"`
	TryupPttReq  []*TryUpPttReq       `protobuf:"bytes,5,rep"`
	GetpttUrlReq []*GetPttUrlReq      `protobuf:"bytes,6,rep"`
	CommandId    proto.Option[uint32] `protobuf:"varint,7,opt"`
	DelImgReq    []*DelImgReq         `protobuf:"bytes,8,rep"`
	Extension    []byte               `protobuf:"bytes,1001,opt"`
}

type D388RspBody struct {
	ClientIp     proto.Option[uint32] `protobuf:"varint,1,opt"`
	Subcmd       proto.Option[uint32] `protobuf:"varint,2,opt"`
	TryupImgRsp  []*D388TryUpImgRsp   `protobuf:"bytes,3,rep"`
	GetimgUrlRsp []*GetImgUrlRsp      `protobuf:"bytes,4,rep"`
	TryupPttRsp  []*TryUpPttRsp       `protobuf:"bytes,5,rep"`
	GetpttUrlRsp []*GetPttUrlRsp      `protobuf:"bytes,6,rep"`
	DelImgRsp    []*DelImgRsp         `protobuf:"bytes,7,rep"`
}

type TryUpImgReq struct {
	GroupCode       proto.Option[uint64] `protobuf:"varint,1,opt"`
	SrcUin          proto.Option[uint64] `protobuf:"varint,2,opt"`
	FileId          proto.Option[uint64] `protobuf:"varint,3,opt"`
	FileMd5         []byte               `protobuf:"bytes,4,opt"`
	FileSize        proto.Option[uint64] `protobuf:"varint,5,opt"`
	FileName        []byte               `protobuf:"bytes,6,opt"`
	SrcTerm         proto.Option[uint32] `protobuf:"varint,7,opt"`
	PlatformType    proto.Option[uint32] `protobuf:"varint,8,opt"`
	BuType          proto.Option[uint32] `protobuf:"varint,9,opt"`
	PicWidth        proto.Option[uint32] `protobuf:"varint,10,opt"`
	PicHeight       proto.Option[uint32] `protobuf:"varint,11,opt"`
	PicType         proto.Option[uint32] `protobuf:"varint,12,opt"`
	BuildVer        []byte               `protobuf:"bytes,13,opt"`
	InnerIp         proto.Option[uint32] `protobuf:"varint,14,opt"`
	AppPicType      proto.Option[uint32] `protobuf:"varint,15,opt"`
	OriginalPic     proto.Option[uint32] `protobuf:"varint,16,opt"`
	FileIndex       []byte               `protobuf:"bytes,17,opt"`
	DstUin          proto.Option[uint64] `protobuf:"varint,18,opt"`
	SrvUpload       proto.Option[uint32] `protobuf:"varint,19,opt"`
	TransferUrl     []byte               `protobuf:"bytes,20,opt"`
	QqmeetGuildId   proto.Option[uint64] `protobuf:"varint,21,opt"`
	QqmeetChannelId proto.Option[uint64] `protobuf:"varint,22,opt"`
}

type D388TryUpImgRsp struct {
	FileId        proto.Option[uint64] `protobuf:"varint,1,opt"`
	Result        proto.Option[uint32] `protobuf:"varint,2,opt"`
	FailMsg       []byte               `protobuf:"bytes,3,opt"`
	FileExit      proto.Option[bool]   `protobuf:"varint,4,opt"`
	ImgInfo       *ImgInfo             `protobuf:"bytes,5,opt"`
	UpIp          []uint32             `protobuf:"varint,6,rep"`
	UpPort        []uint32             `protobuf:"varint,7,rep"`
	UpUkey        []byte               `protobuf:"bytes,8,opt"`
	Fileid        proto.Option[uint64] `protobuf:"varint,9,opt"`
	UpOffset      proto.Option[uint64] `protobuf:"varint,10,opt"`
	BlockSize     proto.Option[uint64] `protobuf:"varint,11,opt"`
	NewBigChan    proto.Option[bool]   `protobuf:"varint,12,opt"`
	UpIp6         []*IPv6Info          `protobuf:"bytes,26,rep"`
	ClientIp6     []byte               `protobuf:"bytes,27,opt"`
	DownloadIndex []byte               `protobuf:"bytes,28,opt"`
	Info4Busi     *TryUpInfo4Busi      `protobuf:"bytes,1001,opt"`
}

type TryUpInfo4Busi struct {
	DownDomain      []byte `protobuf:"bytes,1,opt"`
	ThumbDownUrl    []byte `protobuf:"bytes,2,opt"`
	OriginalDownUrl []byte `protobuf:"bytes,3,opt"`
	BigDownUrl      []byte `protobuf:"bytes,4,opt"`
	FileResid       []byte `protobuf:"bytes,5,opt"`
}

type TryUpPttReq struct {
	GroupCode    proto.Option[uint64] `protobuf:"varint,1,opt"`
	SrcUin       proto.Option[uint64] `protobuf:"varint,2,opt"`
	FileId       proto.Option[uint64] `protobuf:"varint,3,opt"`
	FileMd5      []byte               `protobuf:"bytes,4,opt"`
	FileSize     proto.Option[uint64] `protobuf:"varint,5,opt"`
	FileName     []byte               `protobuf:"bytes,6,opt"`
	SrcTerm      proto.Option[uint32] `protobuf:"varint,7,opt"`
	PlatformType proto.Option[uint32] `protobuf:"varint,8,opt"`
	BuType       proto.Option[uint32] `protobuf:"varint,9,opt"`
	BuildVer     []byte               `protobuf:"bytes,10,opt"`
	InnerIp      proto.Option[uint32] `protobuf:"varint,11,opt"`
	VoiceLength  proto.Option[uint32] `protobuf:"varint,12,opt"`
	NewUpChan    proto.Option[bool]   `protobuf:"varint,13,opt"`
	Codec        proto.Option[uint32] `protobuf:"varint,14,opt"`
	VoiceType    proto.Option[uint32] `protobuf:"varint,15,opt"`
	BuId         proto.Option[uint32] `protobuf:"varint,16,opt"`
}

type TryUpPttRsp struct {
	FileId      proto.Option[uint64] `protobuf:"varint,1,opt"`
	Result      proto.Option[uint32] `protobuf:"varint,2,opt"`
	FailMsg     []byte               `protobuf:"bytes,3,opt"`
	FileExit    proto.Option[bool]   `protobuf:"varint,4,opt"`
	UpIp        []uint32             `protobuf:"varint,5,rep"`
	UpPort      []uint32             `protobuf:"varint,6,rep"`
	UpUkey      []byte               `protobuf:"bytes,7,opt"`
	Fileid      proto.Option[uint64] `protobuf:"varint,8,opt"`
	UpOffset    proto.Option[uint64] `protobuf:"varint,9,opt"`
	BlockSize   proto.Option[uint64] `protobuf:"varint,10,opt"`
	FileKey     []byte               `protobuf:"bytes,11,opt"`
	ChannelType proto.Option[uint32] `protobuf:"varint,12,opt"`
	UpIp6       []*IPv6Info          `protobuf:"bytes,26,rep"`
	ClientIp6   []byte               `protobuf:"bytes,27,opt"`
}
