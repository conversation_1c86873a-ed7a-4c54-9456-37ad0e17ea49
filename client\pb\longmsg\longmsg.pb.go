// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/longmsg/longmsg.proto

package longmsg

type LongMsgDeleteReq struct {
	MsgResid []byte `protobuf:"bytes,1,opt"`
	MsgType  int32  `protobuf:"varint,2,opt"`
}

type LongMsgDeleteRsp struct {
	Result   int32  `protobuf:"varint,1,opt"`
	MsgResid []byte `protobuf:"bytes,2,opt"`
}

type LongMsgDownReq struct {
	SrcUin    int32  `protobuf:"varint,1,opt"`
	MsgResid  []byte `protobuf:"bytes,2,opt"`
	MsgType   int32  `protobuf:"varint,3,opt"`
	NeedCache int32  `protobuf:"varint,4,opt"`
}

type LongMsgDownRsp struct {
	Result     int32  `protobuf:"varint,1,opt"`
	MsgResid   []byte `protobuf:"bytes,2,opt"`
	MsgContent []byte `protobuf:"bytes,3,opt"`
}

type LongMsgUpReq struct {
	MsgType    int32  `protobuf:"varint,1,opt"`
	DstUin     int64  `protobuf:"varint,2,opt"`
	MsgId      int32  `protobuf:"varint,3,opt"`
	MsgContent []byte `protobuf:"bytes,4,opt"`
	StoreType  int32  `protobuf:"varint,5,opt"`
	MsgUkey    []byte `protobuf:"bytes,6,opt"`
	NeedCache  int32  `protobuf:"varint,7,opt"`
}

type LongMsgUpRsp struct {
	Result   int32  `protobuf:"varint,1,opt"`
	MsgId    int32  `protobuf:"varint,2,opt"`
	MsgResid []byte `protobuf:"bytes,3,opt"`
}

type LongReqBody struct {
	Subcmd       int32           `protobuf:"varint,1,opt"`
	TermType     int32           `protobuf:"varint,2,opt"`
	PlatformType int32           `protobuf:"varint,3,opt"`
	MsgUpReq     []*LongMsgUpReq `protobuf:"bytes,4,rep"`
	// repeated LongMsgDownReq msgDownReq = 5;
	// repeated LongMsgDeleteReq msgDelReq = 6;
	AgentType int32 `protobuf:"varint,10,opt"`
}

type LongRspBody struct {
	Subcmd int32 `protobuf:"varint,1,opt"`
	// repeated LongMsgUpRsp msgUpRsp = 2;
	MsgDownRsp []*LongMsgDownRsp `protobuf:"bytes,3,rep"` //repeated LongMsgDeleteRsp msgDelRsp = 4;
}
