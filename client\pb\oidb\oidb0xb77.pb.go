// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0xb77.proto

package oidb

type DB77ReqBody struct {
	AppId       uint64           `protobuf:"varint,1,opt"`
	AppType     uint32           `protobuf:"varint,2,opt"`
	MsgStyle    uint32           `protobuf:"varint,3,opt"`
	SenderUin   uint64           `protobuf:"varint,4,opt"`
	ClientInfo  *DB77ClientInfo  `protobuf:"bytes,5,opt"`
	TextMsg     string           `protobuf:"bytes,6,opt"`
	ExtInfo     *DB77ExtInfo     `protobuf:"bytes,7,opt"`
	SendType    uint32           `protobuf:"varint,10,opt"`
	RecvUin     uint64           `protobuf:"varint,11,opt"`
	RichMsgBody *DB77RichMsgBody `protobuf:"bytes,12,opt"`
	RecvGuildId uint64           `protobuf:"varint,19,opt"`
	_           [0]func()
}

type DB77ClientInfo struct {
	Platform           uint32 `protobuf:"varint,1,opt"`
	SdkVersion         string `protobuf:"bytes,2,opt"`
	AndroidPackageName string `protobuf:"bytes,3,opt"`
	AndroidSignature   string `protobuf:"bytes,4,opt"`
	IosBundleId        string `protobuf:"bytes,5,opt"`
	PcSign             string `protobuf:"bytes,6,opt"`
	_                  [0]func()
}

type DB77ExtInfo struct {
	CustomFeatureId []uint32 `protobuf:"varint,11,rep"`
	ApnsWording     string   `protobuf:"bytes,12,opt"`
	GroupSaveDbFlag uint32   `protobuf:"varint,13,opt"`
	ReceiverAppId   uint32   `protobuf:"varint,14,opt"`
	MsgSeq          uint64   `protobuf:"varint,15,opt"`
}

type DB77RichMsgBody struct {
	Title      string `protobuf:"bytes,10,opt"`
	Summary    string `protobuf:"bytes,11,opt"`
	Brief      string `protobuf:"bytes,12,opt"`
	Url        string `protobuf:"bytes,13,opt"`
	PictureUrl string `protobuf:"bytes,14,opt"`
	Action     string `protobuf:"bytes,15,opt"`
	MusicUrl   string `protobuf:"bytes,16,opt"` //ImageInfo imageInfo = 17;
	_          [0]func()
}
