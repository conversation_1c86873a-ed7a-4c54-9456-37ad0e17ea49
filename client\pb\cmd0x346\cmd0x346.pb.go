// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/cmd0x346/cmd0x346.proto

package cmd0x346

type ApplyCleanTrafficRsp struct {
	RetCode int32  `protobuf:"varint,10,opt"`
	RetMsg  string `protobuf:"bytes,20,opt"`
	_       [0]func()
}

type ApplyCopyFromReq struct {
	SrcUin          int64  `protobuf:"varint,10,opt"`
	SrcGroup        int64  `protobuf:"varint,20,opt"`
	SrcSvcid        int32  `protobuf:"varint,30,opt"`
	SrcParentfolder []byte `protobuf:"bytes,40,opt"`
	SrcUuid         []byte `protobuf:"bytes,50,opt"`
	FileMd5         []byte `protobuf:"bytes,60,opt"`
	DstUin          int64  `protobuf:"varint,70,opt"`
	FileSize        int64  `protobuf:"varint,80,opt"`
	FileName        string `protobuf:"bytes,90,opt"`
	DangerLevel     int32  `protobuf:"varint,100,opt"`
	TotalSpace      int64  `protobuf:"varint,110,opt"`
}

type ApplyCopyFromRsp struct {
	RetCode    int32  `protobuf:"varint,10,opt"`
	RetMsg     string `protobuf:"bytes,20,opt"`
	Uuid       []byte `protobuf:"bytes,30,opt"`
	TotalSpace int64  `protobuf:"varint,40,opt"`
}

type ApplyCopyToReq struct {
	DstId         int64  `protobuf:"varint,10,opt"`
	DstUin        int64  `protobuf:"varint,20,opt"`
	DstSvcid      int32  `protobuf:"varint,30,opt"`
	SrcUin        int64  `protobuf:"varint,40,opt"`
	FileSize      int64  `protobuf:"varint,50,opt"`
	FileName      string `protobuf:"bytes,60,opt"`
	LocalFilepath string `protobuf:"bytes,70,opt"`
	Uuid          []byte `protobuf:"bytes,80,opt"`
}

type ApplyCopyToRsp struct {
	RetCode int32  `protobuf:"varint,10,opt"`
	RetMsg  string `protobuf:"bytes,20,opt"`
	FileKey string `protobuf:"bytes,30,opt"`
	_       [0]func()
}

type ApplyDownloadAbsReq struct {
	Uin  int64  `protobuf:"varint,10,opt"`
	Uuid []byte `protobuf:"bytes,20,opt"`
}

type ApplyDownloadAbsRsp struct {
	RetCode      int32         `protobuf:"varint,10,opt"`
	RetMsg       string        `protobuf:"bytes,20,opt"`
	DownloadInfo *DownloadInfo `protobuf:"bytes,30,opt"`
	_            [0]func()
}

type ApplyDownloadReq struct {
	Uin       int64  `protobuf:"varint,10,opt"`
	Uuid      []byte `protobuf:"bytes,20,opt"`
	OwnerType int32  `protobuf:"varint,30,opt"`
	ExtIntype int32  `protobuf:"varint,500,opt"`
}

type ApplyDownloadRsp struct {
	RetCode      int32         `protobuf:"varint,10,opt"`
	RetMsg       string        `protobuf:"bytes,20,opt"`
	DownloadInfo *DownloadInfo `protobuf:"bytes,30,opt"`
	FileInfo     *FileInfo     `protobuf:"bytes,40,opt"`
	_            [0]func()
}

type ApplyForwardFileReq struct {
	SenderUin   int64  `protobuf:"varint,10,opt"`
	RecverUin   int64  `protobuf:"varint,20,opt"`
	Uuid        []byte `protobuf:"bytes,30,opt"`
	DangerLevel int32  `protobuf:"varint,40,opt"`
	TotalSpace  int64  `protobuf:"varint,50,opt"`
}

type ApplyForwardFileRsp struct {
	RetCode    int32  `protobuf:"varint,10,opt"`
	RetMsg     string `protobuf:"bytes,20,opt"`
	TotalSpace int64  `protobuf:"varint,30,opt"`
	UsedSpace  int64  `protobuf:"varint,40,opt"`
	Uuid       []byte `protobuf:"bytes,50,opt"`
}

type ApplyGetTrafficReq struct {
	_ [0]func()
}

type ApplyGetTrafficRsp struct {
	RetCode     int32  `protobuf:"varint,10,opt"`
	RetMsg      string `protobuf:"bytes,20,opt"`
	UseFileSize int64  `protobuf:"varint,30,opt"`
	UseFileNum  int32  `protobuf:"varint,40,opt"`
	AllFileSize int64  `protobuf:"varint,50,opt"`
	AllFileNum  int32  `protobuf:"varint,60,opt"`
	_           [0]func()
}

type ApplyListDownloadReq struct {
	Uin        int64 `protobuf:"varint,10,opt"`
	BeginIndex int32 `protobuf:"varint,20,opt"`
	ReqCount   int32 `protobuf:"varint,30,opt"`
	_          [0]func()
}

type ApplyListDownloadRsp struct {
	RetCode    int32       `protobuf:"varint,10,opt"`
	RetMsg     string      `protobuf:"bytes,20,opt"`
	TotalCount int32       `protobuf:"varint,30,opt"`
	BeginIndex int32       `protobuf:"varint,40,opt"`
	RspCount   int32       `protobuf:"varint,50,opt"`
	IsEnd      int32       `protobuf:"varint,60,opt"`
	FileList   []*FileInfo `protobuf:"bytes,70,rep"`
}

type ApplyUploadHitReq struct {
	SenderUin     int64  `protobuf:"varint,10,opt"`
	RecverUin     int64  `protobuf:"varint,20,opt"`
	FileSize      int64  `protobuf:"varint,30,opt"`
	FileName      string `protobuf:"bytes,40,opt"`
	Bytes_10MMd5  []byte `protobuf:"bytes,50,opt"`
	LocalFilepath string `protobuf:"bytes,60,opt"`
	DangerLevel   int32  `protobuf:"varint,70,opt"`
	TotalSpace    int64  `protobuf:"varint,80,opt"`
}

type ApplyUploadHitReqV2 struct {
	SenderUin     int64  `protobuf:"varint,10,opt"`
	RecverUin     int64  `protobuf:"varint,20,opt"`
	FileSize      int64  `protobuf:"varint,30,opt"`
	FileName      string `protobuf:"bytes,40,opt"`
	Bytes_10MMd5  []byte `protobuf:"bytes,50,opt"`
	Bytes_3Sha    []byte `protobuf:"bytes,60,opt"`
	Sha           []byte `protobuf:"bytes,70,opt"`
	LocalFilepath string `protobuf:"bytes,80,opt"`
	DangerLevel   int32  `protobuf:"varint,90,opt"`
	TotalSpace    int64  `protobuf:"varint,100,opt"`
}

type ApplyUploadHitReqV3 struct {
	SenderUin     int64  `protobuf:"varint,10,opt"`
	RecverUin     int64  `protobuf:"varint,20,opt"`
	FileSize      int64  `protobuf:"varint,30,opt"`
	FileName      string `protobuf:"bytes,40,opt"`
	Bytes_10MMd5  []byte `protobuf:"bytes,50,opt"`
	Sha           []byte `protobuf:"bytes,60,opt"`
	LocalFilepath string `protobuf:"bytes,70,opt"`
	DangerLevel   int32  `protobuf:"varint,80,opt"`
	TotalSpace    int64  `protobuf:"varint,90,opt"`
}

type ApplyUploadHitRsp struct {
	RetCode      int32  `protobuf:"varint,10,opt"`
	RetMsg       string `protobuf:"bytes,20,opt"`
	UploadIp     string `protobuf:"bytes,30,opt"`
	UploadPort   int32  `protobuf:"varint,40,opt"`
	UploadDomain string `protobuf:"bytes,50,opt"`
	Uuid         []byte `protobuf:"bytes,60,opt"`
	UploadKey    []byte `protobuf:"bytes,70,opt"`
	TotalSpace   int64  `protobuf:"varint,80,opt"`
	UsedSpace    int64  `protobuf:"varint,90,opt"`
}

type ApplyUploadHitRspV2 struct {
	RetCode      int32  `protobuf:"varint,10,opt"`
	RetMsg       string `protobuf:"bytes,20,opt"`
	UploadIp     string `protobuf:"bytes,30,opt"`
	UploadPort   int32  `protobuf:"varint,40,opt"`
	UploadDomain string `protobuf:"bytes,50,opt"`
	Uuid         []byte `protobuf:"bytes,60,opt"`
	UploadKey    []byte `protobuf:"bytes,70,opt"`
	TotalSpace   int64  `protobuf:"varint,80,opt"`
	UsedSpace    int64  `protobuf:"varint,90,opt"`
}

type ApplyUploadHitRspV3 struct {
	RetCode      int32  `protobuf:"varint,10,opt"`
	RetMsg       string `protobuf:"bytes,20,opt"`
	UploadIp     string `protobuf:"bytes,30,opt"`
	UploadPort   int32  `protobuf:"varint,40,opt"`
	UploadDomain string `protobuf:"bytes,50,opt"`
	Uuid         []byte `protobuf:"bytes,60,opt"`
	UploadKey    []byte `protobuf:"bytes,70,opt"`
	TotalSpace   int64  `protobuf:"varint,80,opt"`
	UsedSpace    int64  `protobuf:"varint,90,opt"`
}

type ApplyUploadReq struct {
	SenderUin     int64  `protobuf:"varint,10,opt"`
	RecverUin     int64  `protobuf:"varint,20,opt"`
	FileType      int32  `protobuf:"varint,30,opt"`
	FileSize      int64  `protobuf:"varint,40,opt"`
	FileName      string `protobuf:"bytes,50,opt"`
	Bytes_10MMd5  []byte `protobuf:"bytes,60,opt"`
	LocalFilepath string `protobuf:"bytes,70,opt"`
	DangerLevel   int32  `protobuf:"varint,80,opt"`
	TotalSpace    int64  `protobuf:"varint,90,opt"`
}

type ApplyUploadReqV2 struct {
	SenderUin     int64  `protobuf:"varint,10,opt"`
	RecverUin     int64  `protobuf:"varint,20,opt"`
	FileSize      int64  `protobuf:"varint,30,opt"`
	FileName      string `protobuf:"bytes,40,opt"`
	Bytes_10MMd5  []byte `protobuf:"bytes,50,opt"`
	Bytes_3Sha    []byte `protobuf:"bytes,60,opt"`
	LocalFilepath string `protobuf:"bytes,70,opt"`
	DangerLevel   int32  `protobuf:"varint,80,opt"`
	TotalSpace    int64  `protobuf:"varint,90,opt"`
}

type ApplyUploadReqV3 struct {
	SenderUin     int64  `protobuf:"varint,10,opt"`
	RecverUin     int64  `protobuf:"varint,20,opt"`
	FileSize      int64  `protobuf:"varint,30,opt"`
	FileName      string `protobuf:"bytes,40,opt"`
	Bytes_10MMd5  []byte `protobuf:"bytes,50,opt"`
	Sha           []byte `protobuf:"bytes,60,opt"`
	LocalFilepath string `protobuf:"bytes,70,opt"`
	DangerLevel   int32  `protobuf:"varint,80,opt"`
	TotalSpace    int64  `protobuf:"varint,90,opt"`
	Md5           []byte `protobuf:"bytes,110,opt"`
	X3Sha         []byte `protobuf:"bytes,120,opt"`
}

type ApplyUploadRsp struct {
	RetCode       int32    `protobuf:"varint,10,opt"`
	RetMsg        string   `protobuf:"bytes,20,opt"`
	TotalSpace    int64    `protobuf:"varint,30,opt"`
	UsedSpace     int64    `protobuf:"varint,40,opt"`
	UploadedSize  int64    `protobuf:"varint,50,opt"`
	UploadIp      string   `protobuf:"bytes,60,opt"`
	UploadDomain  string   `protobuf:"bytes,70,opt"`
	UploadPort    int32    `protobuf:"varint,80,opt"`
	Uuid          []byte   `protobuf:"bytes,90,opt"`
	UploadKey     []byte   `protobuf:"bytes,100,opt"`
	BoolFileExist bool     `protobuf:"varint,110,opt"`
	PackSize      int32    `protobuf:"varint,120,opt"`
	UploadipList  []string `protobuf:"bytes,130,rep"`
}

type ApplyUploadRspV2 struct {
	RetCode       int32    `protobuf:"varint,10,opt"`
	RetMsg        string   `protobuf:"bytes,20,opt"`
	TotalSpace    int64    `protobuf:"varint,30,opt"`
	UsedSpace     int64    `protobuf:"varint,40,opt"`
	UploadedSize  int64    `protobuf:"varint,50,opt"`
	UploadIp      string   `protobuf:"bytes,60,opt"`
	UploadDomain  string   `protobuf:"bytes,70,opt"`
	UploadPort    int32    `protobuf:"varint,80,opt"`
	Uuid          []byte   `protobuf:"bytes,90,opt"`
	UploadKey     []byte   `protobuf:"bytes,100,opt"`
	BoolFileExist bool     `protobuf:"varint,110,opt"`
	PackSize      int32    `protobuf:"varint,120,opt"`
	UploadipList  []string `protobuf:"bytes,130,rep"`
	HttpsvrApiVer int32    `protobuf:"varint,140,opt"`
	Sha           []byte   `protobuf:"bytes,141,opt"`
}

type ApplyUploadRspV3 struct {
	RetCode                 int32    `protobuf:"varint,10,opt"`
	RetMsg                  string   `protobuf:"bytes,20,opt"`
	TotalSpace              int64    `protobuf:"varint,30,opt"`
	UsedSpace               int64    `protobuf:"varint,40,opt"`
	UploadedSize            int64    `protobuf:"varint,50,opt"`
	UploadIp                string   `protobuf:"bytes,60,opt"`
	UploadDomain            string   `protobuf:"bytes,70,opt"`
	UploadPort              int32    `protobuf:"varint,80,opt"`
	Uuid                    []byte   `protobuf:"bytes,90,opt"`
	UploadKey               []byte   `protobuf:"bytes,100,opt"`
	BoolFileExist           bool     `protobuf:"varint,110,opt"`
	PackSize                int32    `protobuf:"varint,120,opt"`
	UploadIpList            []string `protobuf:"bytes,130,rep"`
	UploadHttpsPort         int32    `protobuf:"varint,140,opt"`
	UploadHttpsDomain       string   `protobuf:"bytes,150,opt"`
	UploadDns               string   `protobuf:"bytes,160,opt"`
	UploadLanip             string   `protobuf:"bytes,170,opt"`
	MediaPlateformUploadKey []byte   `protobuf:"bytes,220,opt"`
}

type DelMessageReq struct {
	UinSender   int64 `protobuf:"varint,1,opt"`
	UinReceiver int64 `protobuf:"varint,2,opt"`
	Time        int32 `protobuf:"varint,10,opt"`
	Random      int32 `protobuf:"varint,20,opt"`
	SeqNo       int32 `protobuf:"varint,30,opt"`
	_           [0]func()
}

type DeleteFileReq struct {
	Uin        int64  `protobuf:"varint,10,opt"`
	PeerUin    int64  `protobuf:"varint,20,opt"`
	DeleteType int32  `protobuf:"varint,30,opt"`
	Uuid       []byte `protobuf:"bytes,40,opt"`
}

type DeleteFileRsp struct {
	RetCode int32  `protobuf:"varint,10,opt"`
	RetMsg  string `protobuf:"bytes,20,opt"`
	_       [0]func()
}

type DownloadInfo struct {
	DownloadKey    []byte   `protobuf:"bytes,10,opt"`
	DownloadIp     string   `protobuf:"bytes,20,opt"`
	DownloadDomain string   `protobuf:"bytes,30,opt"`
	Port           int32    `protobuf:"varint,40,opt"`
	DownloadUrl    string   `protobuf:"bytes,50,opt"`
	DownloadipList []string `protobuf:"bytes,60,rep"`
	Cookie         string   `protobuf:"bytes,70,opt"`
}

type DownloadSuccReq struct {
	Uin  int64  `protobuf:"varint,10,opt"`
	Uuid []byte `protobuf:"bytes,20,opt"`
}

type DownloadSuccRsp struct {
	RetCode  int32  `protobuf:"varint,10,opt"`
	RetMsg   string `protobuf:"bytes,20,opt"`
	DownStat int32  `protobuf:"varint,30,opt"`
	_        [0]func()
}

type ExtensionReq struct {
	Id               int64          `protobuf:"varint,1,opt"`
	Type             int64          `protobuf:"varint,2,opt"`
	DstPhonenum      string         `protobuf:"bytes,3,opt"`
	PhoneConvertType int32          `protobuf:"varint,4,opt"`
	Sig              []byte         `protobuf:"bytes,20,opt"`
	RouteId          int64          `protobuf:"varint,100,opt"`
	DelMessageReq    *DelMessageReq `protobuf:"bytes,90100,opt"`
	DownloadUrlType  int32          `protobuf:"varint,90200,opt"`
	PttFormat        int32          `protobuf:"varint,90300,opt"`
	IsNeedInnerIp    int32          `protobuf:"varint,90400,opt"`
	NetType          int32          `protobuf:"varint,90500,opt"`
	VoiceType        int32          `protobuf:"varint,90600,opt"`
	FileType         int32          `protobuf:"varint,90700,opt"`
	PttTime          int32          `protobuf:"varint,90800,opt"`
}

type ExtensionRsp struct {
	_ [0]func()
}

type FileInfo struct {
	Uin          int64  `protobuf:"varint,1,opt"`
	DangerEvel   int32  `protobuf:"varint,2,opt"`
	FileSize     int64  `protobuf:"varint,3,opt"`
	LifeTime     int32  `protobuf:"varint,4,opt"`
	UploadTime   int32  `protobuf:"varint,5,opt"`
	Uuid         []byte `protobuf:"bytes,6,opt"`
	FileName     string `protobuf:"bytes,7,opt"`
	AbsFileType  int32  `protobuf:"varint,90,opt"`
	Bytes_10MMd5 []byte `protobuf:"bytes,100,opt"`
	Sha          []byte `protobuf:"bytes,101,opt"`
	ClientType   int32  `protobuf:"varint,110,opt"`
	OwnerUin     int64  `protobuf:"varint,120,opt"`
	PeerUin      int64  `protobuf:"varint,121,opt"`
	ExpireTime   int32  `protobuf:"varint,130,opt"`
}

type FileQueryReq struct {
	Uin  int64  `protobuf:"varint,10,opt"`
	Uuid []byte `protobuf:"bytes,20,opt"`
}

type FileQueryRsp struct {
	RetCode  int32     `protobuf:"varint,10,opt"`
	RetMsg   string    `protobuf:"bytes,20,opt"`
	FileInfo *FileInfo `protobuf:"bytes,30,opt"`
	_        [0]func()
}

type RecallFileReq struct {
	Uin  int64  `protobuf:"varint,1,opt"`
	Uuid []byte `protobuf:"bytes,2,opt"`
}

type RecallFileRsp struct {
	RetCode int32  `protobuf:"varint,1,opt"`
	RetMsg  string `protobuf:"bytes,2,opt"`
	_       [0]func()
}

type RecvListQueryReq struct {
	Uin        int64 `protobuf:"varint,1,opt"`
	BeginIndex int32 `protobuf:"varint,2,opt"`
	ReqCount   int32 `protobuf:"varint,3,opt"`
	_          [0]func()
}

type RecvListQueryRsp struct {
	RetCode      int32       `protobuf:"varint,1,opt"`
	RetMsg       string      `protobuf:"bytes,2,opt"`
	FileTotCount int32       `protobuf:"varint,3,opt"`
	BeginIndex   int32       `protobuf:"varint,4,opt"`
	RspFileCount int32       `protobuf:"varint,5,opt"`
	IsEnd        int32       `protobuf:"varint,6,opt"`
	FileList     []*FileInfo `protobuf:"bytes,7,rep"`
}

type RenewFileReq struct {
	Uin    int64  `protobuf:"varint,1,opt"`
	Uuid   []byte `protobuf:"bytes,2,opt"`
	AddTtl int32  `protobuf:"varint,3,opt"`
}

type RenewFileRsp struct {
	RetCode int32  `protobuf:"varint,1,opt"`
	RetMsg  string `protobuf:"bytes,2,opt"`
	_       [0]func()
}

type C346ReqBody struct {
	Cmd int32 `protobuf:"varint,1,opt"`
	Seq int32 `protobuf:"varint,2,opt"`
	// RecvListQueryReq recvListQueryReq = 3;
	// SendListQueryReq sendListQueryReq = 4;
	// RenewFileReq renewFileReq = 5;
	// RecallFileReq recallFileReq = 6;
	ApplyUploadReq *ApplyUploadReq `protobuf:"bytes,7,opt"`
	// ApplyUploadHitReq applyUploadHitReq = 8;
	// ApplyForwardFileReq applyForwardFileReq = 9;
	UploadSuccReq *UploadSuccReq `protobuf:"bytes,10,opt"`
	// DeleteFileReq deleteFileReq = 11;
	// DownloadSuccReq downloadSuccReq = 12;
	// ApplyDownloadAbsReq applyDownloadAbsReq = 13;
	ApplyDownloadReq *ApplyDownloadReq `protobuf:"bytes,14,opt"`
	// ApplyListDownloadReq applyListDownloadReq = 15;
	// FileQueryReq fileQueryReq = 16;
	// ApplyCopyFromReq applyCopyFromReq = 17;
	// ApplyUploadReqV2 applyUploadReqV2 = 18;
	ApplyUploadReqV3 *ApplyUploadReqV3 `protobuf:"bytes,19,opt"`
	// ApplyUploadHitReqV2 applyUploadHitReqV2 = 20;
	// ApplyUploadHitReqV3 applyUploadHitReqV3 = 21;
	BusinessId               int32  `protobuf:"varint,101,opt"`
	ClientType               int32  `protobuf:"varint,102,opt"`
	FlagSupportMediaplatform uint32 `protobuf:"varint,200,opt"`
	// ApplyCopyToReq applyCopyToReq = 90000;
	// ApplyCleanTrafficReq applyCleanTrafficReq = 90001; empty message
	// ApplyGetTrafficReq applyGetTrafficReq = 90002;
	ExtensionReq *ExtensionReq `protobuf:"bytes,99999,opt"`
	_            [0]func()
}

type C346RspBody struct {
	Cmd int32 `protobuf:"varint,1,opt"`
	Seq int32 `protobuf:"varint,2,opt"`
	// RecvListQueryRsp recvListQueryRsp = 3;
	// SendListQueryRsp sendListQueryRsp = 4;
	// RenewFileRsp renewFileRsp = 5;
	// RecallFileRsp recallFileRsp = 6;
	ApplyUploadRsp *ApplyUploadRsp `protobuf:"bytes,7,opt"`
	// ApplyUploadHitRsp applyUploadHitRsp = 8;
	// ApplyForwardFileRsp applyForwardFileRsp = 9;
	// UploadSuccRsp uploadSuccRsp = 10;
	// DeleteFileRsp deleteFileRsp = 11;
	// DownloadSuccRsp downloadSuccRsp = 12;
	// ApplyDownloadAbsRsp applyDownloadAbsRsp = 13;
	ApplyDownloadRsp *ApplyDownloadRsp `protobuf:"bytes,14,opt"`
	// ApplyListDownloadRsp applyListDownloadRsp = 15;
	// FileQueryRsp fileQueryRsp = 16;
	// ApplyCopyFromRsp applyCopyFromRsp = 17;
	// ApplyUploadRspV2 applyUploadRspV2 = 18;
	ApplyUploadRspV3 *ApplyUploadRspV3 `protobuf:"bytes,19,opt"`
	// ApplyUploadHitRspV2 applyUploadHitRspV2 = 20;
	// ApplyUploadHitRspV3 applyUploadHitRspV3 = 21;
	BusinessId int32 `protobuf:"varint,101,opt"`
	ClientType int32 `protobuf:"varint,102,opt"`
	_          [0]func()
}

type SendListQueryReq struct {
	Uin        int64 `protobuf:"varint,1,opt"`
	BeginIndex int32 `protobuf:"varint,2,opt"`
	ReqCount   int32 `protobuf:"varint,3,opt"`
	_          [0]func()
}

type SendListQueryRsp struct {
	RetCode      int32       `protobuf:"varint,1,opt"`
	RetMsg       string      `protobuf:"bytes,2,opt"`
	FileTotCount int32       `protobuf:"varint,3,opt"`
	BeginIndex   int32       `protobuf:"varint,4,opt"`
	RspFileCount int32       `protobuf:"varint,5,opt"`
	IsEnd        int32       `protobuf:"varint,6,opt"`
	TotLimit     int64       `protobuf:"varint,7,opt"`
	UsedLimit    int64       `protobuf:"varint,8,opt"`
	FileList     []*FileInfo `protobuf:"bytes,9,rep"`
}

type UploadSuccReq struct {
	SenderUin int64  `protobuf:"varint,10,opt"`
	RecverUin int64  `protobuf:"varint,20,opt"`
	Uuid      []byte `protobuf:"bytes,30,opt"`
}

type UploadSuccRsp struct {
	RetCode  int32     `protobuf:"varint,10,opt"`
	RetMsg   string    `protobuf:"bytes,20,opt"`
	FileInfo *FileInfo `protobuf:"bytes,30,opt"`
	_        [0]func()
}
