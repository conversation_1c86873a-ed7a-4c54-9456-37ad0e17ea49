#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键词匹配回复插件
支持精确匹配、模糊匹配、正则匹配等多种匹配模式
"""

import json
import re
import os
from typing import Dict, List, Any
from pathlib import Path

from nonebot import on_message, on_command
from nonebot.adapters.onebot.v11 import Bot, Event, GroupMessageEvent, PrivateMessageEvent
from nonebot.adapters.onebot.v11.message import Message
from nonebot.params import CommandArg
from nonebot.permission import SUPERUSER

# 数据文件路径
DATA_DIR = Path("data")
KEYWORDS_FILE = DATA_DIR / "keywords.json"

# 确保数据目录存在
DATA_DIR.mkdir(exist_ok=True)

class KeywordManager:
    """关键词管理器"""
    
    def __init__(self):
        self.keywords = self.load_keywords()
    
    def load_keywords(self) -> Dict[str, Any]:
        """加载关键词数据"""
        if KEYWORDS_FILE.exists():
            try:
                with open(KEYWORDS_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载关键词文件失败: {e}")
        
        # 默认关键词数据
        default_keywords = {
            "exact": {  # 精确匹配
                "你好": ["你好！", "Hi！", "欢迎！"],
                "再见": ["再见！", "拜拜！", "下次见！"],
                "谢谢": ["不客气！", "不用谢！", "很高兴帮到你！"]
            },
            "fuzzy": {  # 模糊匹配（包含关键词即可）
                "天气": ["今天天气不错呢！", "记得关注天气变化哦！"],
                "吃饭": ["记得按时吃饭！", "要好好吃饭哦！"],
                "学习": ["学习使人进步！", "加油学习！", "知识就是力量！"]
            },
            "regex": {  # 正则匹配
                r"(\d+)\+(\d+)": "计算结果是: {result}",  # 简单加法计算
                r"时间|几点": "现在是 {time}"
            }
        }
        
        self.save_keywords(default_keywords)
        return default_keywords
    
    def save_keywords(self, keywords: Dict[str, Any]):
        """保存关键词数据"""
        try:
            with open(KEYWORDS_FILE, 'w', encoding='utf-8') as f:
                json.dump(keywords, f, ensure_ascii=False, indent=2)
            self.keywords = keywords
        except Exception as e:
            print(f"保存关键词文件失败: {e}")
    
    def add_keyword(self, match_type: str, keyword: str, replies: List[str]) -> bool:
        """添加关键词"""
        if match_type not in self.keywords:
            self.keywords[match_type] = {}
        
        self.keywords[match_type][keyword] = replies
        self.save_keywords(self.keywords)
        return True
    
    def remove_keyword(self, match_type: str, keyword: str) -> bool:
        """删除关键词"""
        if match_type in self.keywords and keyword in self.keywords[match_type]:
            del self.keywords[match_type][keyword]
            self.save_keywords(self.keywords)
            return True
        return False
    
    def match_message(self, message: str) -> str:
        """匹配消息并返回回复"""
        import random
        from datetime import datetime
        
        # 1. 精确匹配
        if message in self.keywords.get("exact", {}):
            replies = self.keywords["exact"][message]
            return random.choice(replies)
        
        # 2. 模糊匹配
        for keyword, replies in self.keywords.get("fuzzy", {}).items():
            if keyword in message:
                return random.choice(replies)
        
        # 3. 正则匹配
        for pattern, reply_template in self.keywords.get("regex", {}).items():
            match = re.search(pattern, message)
            if match:
                if "(\d+)\+(\d+)" in pattern:
                    # 简单加法计算
                    num1, num2 = match.groups()
                    result = int(num1) + int(num2)
                    return reply_template.format(result=result)
                elif "时间|几点" in pattern:
                    # 时间查询
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    return reply_template.format(time=current_time)
                else:
                    return reply_template
        
        return ""

# 创建关键词管理器实例
keyword_manager = KeywordManager()

# 消息处理器
message_handler = on_message(priority=10, block=False)

@message_handler.handle()
async def handle_message(bot: Bot, event: Event):
    """处理消息"""
    message = str(event.get_message()).strip()
    
    # 获取回复
    reply = keyword_manager.match_message(message)
    
    if reply:
        await message_handler.send(reply)

# 管理命令
add_keyword_cmd = on_command("添加关键词", permission=SUPERUSER, priority=1)
remove_keyword_cmd = on_command("删除关键词", permission=SUPERUSER, priority=1)
list_keywords_cmd = on_command("关键词列表", permission=SUPERUSER, priority=1)

@add_keyword_cmd.handle()
async def add_keyword(bot: Bot, event: Event, args: Message = CommandArg()):
    """添加关键词命令"""
    args_text = str(args).strip()
    
    if not args_text:
        await add_keyword_cmd.send("用法: /添加关键词 <匹配类型> <关键词> <回复1> [回复2] [回复3]...")
        await add_keyword_cmd.send("匹配类型: exact(精确) fuzzy(模糊) regex(正则)")
        return
    
    parts = args_text.split()
    if len(parts) < 3:
        await add_keyword_cmd.send("参数不足！用法: /添加关键词 <匹配类型> <关键词> <回复1> [回复2]...")
        return
    
    match_type = parts[0]
    keyword = parts[1]
    replies = parts[2:]
    
    if match_type not in ["exact", "fuzzy", "regex"]:
        await add_keyword_cmd.send("匹配类型错误！支持: exact(精确) fuzzy(模糊) regex(正则)")
        return
    
    if keyword_manager.add_keyword(match_type, keyword, replies):
        await add_keyword_cmd.send(f"成功添加关键词: {keyword}")
    else:
        await add_keyword_cmd.send("添加关键词失败！")

@remove_keyword_cmd.handle()
async def remove_keyword(bot: Bot, event: Event, args: Message = CommandArg()):
    """删除关键词命令"""
    args_text = str(args).strip()
    
    if not args_text:
        await remove_keyword_cmd.send("用法: /删除关键词 <匹配类型> <关键词>")
        return
    
    parts = args_text.split()
    if len(parts) != 2:
        await remove_keyword_cmd.send("参数错误！用法: /删除关键词 <匹配类型> <关键词>")
        return
    
    match_type, keyword = parts
    
    if keyword_manager.remove_keyword(match_type, keyword):
        await remove_keyword_cmd.send(f"成功删除关键词: {keyword}")
    else:
        await remove_keyword_cmd.send("关键词不存在！")

@list_keywords_cmd.handle()
async def list_keywords(bot: Bot, event: Event):
    """列出所有关键词"""
    keywords = keyword_manager.keywords
    
    if not any(keywords.values()):
        await list_keywords_cmd.send("暂无关键词")
        return
    
    result = "📝 关键词列表:\n\n"
    
    for match_type, kw_dict in keywords.items():
        if kw_dict:
            type_name = {"exact": "精确匹配", "fuzzy": "模糊匹配", "regex": "正则匹配"}.get(match_type, match_type)
            result += f"🔸 {type_name}:\n"
            for keyword, replies in kw_dict.items():
                if isinstance(replies, list):
                    replies_text = " | ".join(replies)
                else:
                    replies_text = str(replies)
                result += f"  • {keyword} → {replies_text}\n"
            result += "\n"
    
    await list_keywords_cmd.send(result.strip())
