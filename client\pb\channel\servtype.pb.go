// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/channel/servtype.proto

package channel

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type AppChannelMsg struct {
	Summary      proto.Option[string] `protobuf:"bytes,1,opt"`
	Msg          proto.Option[string] `protobuf:"bytes,2,opt"`
	ExpireTimeMs proto.Option[uint64] `protobuf:"varint,3,opt"`
	SchemaType   proto.Option[uint32] `protobuf:"varint,4,opt"`
	Schema       proto.Option[string] `protobuf:"bytes,5,opt"`
	_            [0]func()
}

type CategoryChannelInfo struct {
	ChannelIndex proto.Option[uint32] `protobuf:"varint,1,opt"`
	ChannelId    proto.Option[uint64] `protobuf:"varint,2,opt"`
	_            [0]func()
}

type CategoryInfo struct {
	CategoryIndex proto.Option[uint32]   `protobuf:"varint,1,opt"`
	ChannelInfo   []*CategoryChannelInfo `protobuf:"bytes,2,rep"`
	CategoryName  []byte                 `protobuf:"bytes,3,opt"`
	CategoryId    proto.Option[uint64]   `protobuf:"varint,4,opt"`
}

type ChanInfoFilter struct {
	ChannelName            proto.Option[uint32]    `protobuf:"varint,2,opt"`
	CreatorId              proto.Option[uint32]    `protobuf:"varint,3,opt"`
	CreateTime             proto.Option[uint32]    `protobuf:"varint,4,opt"`
	GuildId                proto.Option[uint32]    `protobuf:"varint,5,opt"`
	MsgNotifyType          proto.Option[uint32]    `protobuf:"varint,6,opt"`
	ChannelType            proto.Option[uint32]    `protobuf:"varint,7,opt"`
	SpeakPermission        proto.Option[uint32]    `protobuf:"varint,8,opt"`
	LastMsgSeq             proto.Option[uint32]    `protobuf:"varint,11,opt"`
	LastCntMsgSeq          proto.Option[uint32]    `protobuf:"varint,12,opt"`
	VoiceChannelInfoFilter *VoiceChannelInfoFilter `protobuf:"bytes,14,opt"`
	LiveChannelInfoFilter  *LiveChannelInfoFilter  `protobuf:"bytes,15,opt"`
	BannedSpeak            proto.Option[uint32]    `protobuf:"varint,16,opt"`
	_                      [0]func()
}

type ChangeChanInfo struct {
	GuildId        proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChanId         proto.Option[uint64] `protobuf:"varint,2,opt"`
	OperatorId     proto.Option[uint64] `protobuf:"varint,3,opt"`
	InfoSeq        *MsgSeq              `protobuf:"bytes,4,opt"`
	UpdateType     proto.Option[uint32] `protobuf:"varint,5,opt"`
	ChanInfoFilter *ChanInfoFilter      `protobuf:"bytes,6,opt"`
	ChanInfo       *ServChannelInfo     `protobuf:"bytes,7,opt"`
	_              [0]func()
}

type ChangeGuildInfo struct {
	GuildId         proto.Option[uint64] `protobuf:"varint,1,opt"`
	OperatorId      proto.Option[uint64] `protobuf:"varint,2,opt"`
	InfoSeq         *MsgSeq              `protobuf:"bytes,3,opt"`
	FaceSeq         *MsgSeq              `protobuf:"bytes,4,opt"`
	UpdateType      proto.Option[uint32] `protobuf:"varint,5,opt"`
	GuildInfoFilter *GuildInfoFilter     `protobuf:"bytes,6,opt"`
	GuildInfo       *GuildInfo           `protobuf:"bytes,7,opt"`
	_               [0]func()
}

type ChannelID struct {
	ChanId proto.Option[uint64] `protobuf:"varint,1,opt"`
	_      [0]func()
}

type ServChannelInfo struct {
	ChannelId        proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChannelName      []byte               `protobuf:"bytes,2,opt"`
	CreatorId        proto.Option[uint64] `protobuf:"varint,3,opt"`
	CreateTime       proto.Option[uint64] `protobuf:"varint,4,opt"`
	GuildId          proto.Option[uint64] `protobuf:"varint,5,opt"`
	MsgNotifyType    proto.Option[uint32] `protobuf:"varint,6,opt"`
	ChannelType      proto.Option[uint32] `protobuf:"varint,7,opt"`
	SpeakPermission  proto.Option[uint32] `protobuf:"varint,8,opt"`
	LastMsgSeq       *MsgSeq              `protobuf:"bytes,11,opt"`
	LastCntMsgSeq    *MsgSeq              `protobuf:"bytes,12,opt"`
	VoiceChannelInfo *VoiceChannelInfo    `protobuf:"bytes,14,opt"`
	LiveChannelInfo  *LiveChannelInfo     `protobuf:"bytes,15,opt"`
	BannedSpeak      proto.Option[uint32] `protobuf:"varint,16,opt"`
}

type CommGrayTips struct {
	BusiType   proto.Option[uint64]       `protobuf:"varint,1,opt"`
	BusiId     proto.Option[uint64]       `protobuf:"varint,2,opt"`
	CtrlFlag   proto.Option[uint32]       `protobuf:"varint,3,opt"`
	TemplId    proto.Option[uint64]       `protobuf:"varint,4,opt"`
	TemplParam []*CommGrayTips_TemplParam `protobuf:"bytes,5,rep"`
	Content    []byte                     `protobuf:"bytes,6,opt"`
	TipsSeqId  proto.Option[uint64]       `protobuf:"varint,10,opt"`
	PbReserv   []byte                     `protobuf:"bytes,100,opt"`
}

type CreateChan struct {
	GuildId    proto.Option[uint64] `protobuf:"varint,1,opt"`
	OperatorId proto.Option[uint64] `protobuf:"varint,3,opt"`
	CreateId   []*ChannelID         `protobuf:"bytes,4,rep"`
}

type CreateGuild struct {
	OperatorId proto.Option[uint64] `protobuf:"varint,1,opt"`
	GuildId    proto.Option[uint64] `protobuf:"varint,2,opt"`
	_          [0]func()
}

type DestroyChan struct {
	GuildId    proto.Option[uint64] `protobuf:"varint,1,opt"`
	OperatorId proto.Option[uint64] `protobuf:"varint,3,opt"`
	DeleteId   []*ChannelID         `protobuf:"bytes,4,rep"`
}

type DestroyGuild struct {
	OperatorId proto.Option[uint64] `protobuf:"varint,1,opt"`
	GuildId    proto.Option[uint64] `protobuf:"varint,2,opt"`
	_          [0]func()
}

type EventBody struct {
	ReadNotify           *ReadNotify              `protobuf:"bytes,1,opt"`
	CommGrayTips         *CommGrayTips            `protobuf:"bytes,2,opt"`
	CreateGuild          *CreateGuild             `protobuf:"bytes,3,opt"`
	DestroyGuild         *DestroyGuild            `protobuf:"bytes,4,opt"`
	JoinGuild            *JoinGuild               `protobuf:"bytes,5,opt"`
	KickOffGuild         *KickOffGuild            `protobuf:"bytes,6,opt"`
	QuitGuild            *QuitGuild               `protobuf:"bytes,7,opt"`
	ChangeGuildInfo      *ChangeGuildInfo         `protobuf:"bytes,8,opt"`
	CreateChan           *CreateChan              `protobuf:"bytes,9,opt"`
	DestroyChan          *DestroyChan             `protobuf:"bytes,10,opt"`
	ChangeChanInfo       *ChangeChanInfo          `protobuf:"bytes,11,opt"`
	SetAdmin             *SetAdmin                `protobuf:"bytes,12,opt"`
	SetMsgRecvType       *SetMsgRecvType          `protobuf:"bytes,13,opt"`
	UpdateMsg            *UpdateMsg               `protobuf:"bytes,14,opt"`
	SetTop               *SetTop                  `protobuf:"bytes,17,opt"`
	SwitchChannel        *SwitchVoiceChannel      `protobuf:"bytes,18,opt"`
	UpdateCategory       *UpdateCategory          `protobuf:"bytes,21,opt"`
	UpdateVoiceBlockList *UpdateVoiceBlockList    `protobuf:"bytes,22,opt"`
	SetMute              *SetMute                 `protobuf:"bytes,23,opt"`
	LiveStatusChangeRoom *LiveRoomStatusChangeMsg `protobuf:"bytes,24,opt"`
	SwitchLiveRoom       *SwitchLiveRoom          `protobuf:"bytes,25,opt"`
	Events               []*MsgEvent              `protobuf:"bytes,39,rep"`
	Scheduler            *SchedulerMsg            `protobuf:"bytes,40,opt"`
	AppChannel           *AppChannelMsg           `protobuf:"bytes,41,opt"`
	FeedEvent            *FeedEvent               `protobuf:"bytes,44,opt"`
	WeakMsgAppChannel    *AppChannelMsg           `protobuf:"bytes,46,opt"`
	ReadFeedNotify       *ReadFeedNotify          `protobuf:"bytes,48,opt"`
}

type FeedEvent struct {
	GuildId    proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChannelId  proto.Option[uint64] `protobuf:"varint,2,opt"`
	FeedId     proto.Option[string] `protobuf:"bytes,3,opt"`
	MsgSummary proto.Option[string] `protobuf:"bytes,4,opt"`
	EventTime  proto.Option[uint64] `protobuf:"varint,5,opt"`
	_          [0]func()
}

type ReadFeedNotify struct {
	ReportTime proto.Option[uint64] `protobuf:"varint,2,opt"`
	_          [0]func()
}

type GroupProStatus struct {
	IsEnable proto.Option[uint32] `protobuf:"varint,1,opt"`
	IsBanned proto.Option[uint32] `protobuf:"varint,2,opt"`
	IsFrozen proto.Option[uint32] `protobuf:"varint,3,opt"`
	_        [0]func()
}

type GuildInfo struct {
	GuildCode          proto.Option[uint64] `protobuf:"varint,2,opt"`
	OwnerId            proto.Option[uint64] `protobuf:"varint,3,opt"`
	CreateTime         proto.Option[uint64] `protobuf:"varint,4,opt"`
	MemberMaxNum       proto.Option[uint32] `protobuf:"varint,5,opt"`
	MemberNum          proto.Option[uint32] `protobuf:"varint,6,opt"`
	GuildType          proto.Option[uint32] `protobuf:"varint,7,opt"`
	GuildName          []byte               `protobuf:"bytes,8,opt"`
	RobotList          []uint64             `protobuf:"varint,9,rep"`
	AdminList          []uint64             `protobuf:"varint,10,rep"`
	RobotMaxNum        proto.Option[uint32] `protobuf:"varint,11,opt"`
	AdminMaxNum        proto.Option[uint32] `protobuf:"varint,12,opt"`
	Profile            []byte               `protobuf:"bytes,13,opt"`
	FaceSeq            proto.Option[uint64] `protobuf:"varint,14,opt"`
	GuildStatus        *GroupProStatus      `protobuf:"bytes,15,opt"`
	ChannelNum         proto.Option[uint32] `protobuf:"varint,16,opt"`
	MemberChangeSeq    *MsgSeq              `protobuf:"bytes,5002,opt"`
	GuildInfoChangeSeq *MsgSeq              `protobuf:"bytes,5003,opt"`
	ChannelChangeSeq   *MsgSeq              `protobuf:"bytes,5004,opt"`
}

type GuildInfoFilter struct {
	GuildCode          proto.Option[uint32] `protobuf:"varint,2,opt"`
	OwnerId            proto.Option[uint32] `protobuf:"varint,3,opt"`
	CreateTime         proto.Option[uint32] `protobuf:"varint,4,opt"`
	MemberMaxNum       proto.Option[uint32] `protobuf:"varint,5,opt"`
	MemberNum          proto.Option[uint32] `protobuf:"varint,6,opt"`
	GuildType          proto.Option[uint32] `protobuf:"varint,7,opt"`
	GuildName          proto.Option[uint32] `protobuf:"varint,8,opt"`
	RobotList          proto.Option[uint32] `protobuf:"varint,9,opt"`
	AdminList          proto.Option[uint32] `protobuf:"varint,10,opt"`
	RobotMaxNum        proto.Option[uint32] `protobuf:"varint,11,opt"`
	AdminMaxNum        proto.Option[uint32] `protobuf:"varint,12,opt"`
	Profile            proto.Option[uint32] `protobuf:"varint,13,opt"`
	FaceSeq            proto.Option[uint32] `protobuf:"varint,14,opt"`
	GuildStatus        proto.Option[uint32] `protobuf:"varint,15,opt"`
	ChannelNum         proto.Option[uint32] `protobuf:"varint,16,opt"`
	MemberChangeSeq    proto.Option[uint32] `protobuf:"varint,5002,opt"`
	GuildInfoChangeSeq proto.Option[uint32] `protobuf:"varint,5003,opt"`
	ChannelChangeSeq   proto.Option[uint32] `protobuf:"varint,5004,opt"`
	_                  [0]func()
}

type JoinGuild struct {
	MemberId     proto.Option[uint64] `protobuf:"varint,3,opt"`
	MemberType   proto.Option[uint32] `protobuf:"varint,4,opt"`
	MemberTinyid proto.Option[uint64] `protobuf:"varint,5,opt"`
	_            [0]func()
}

type KickOffGuild struct {
	MemberId     proto.Option[uint64] `protobuf:"varint,3,opt"`
	SetBlack     proto.Option[uint32] `protobuf:"varint,4,opt"`
	MemberTinyid proto.Option[uint64] `protobuf:"varint,5,opt"`
	_            [0]func()
}

type LiveChannelInfo struct {
	RoomId    proto.Option[uint64] `protobuf:"varint,1,opt"`
	AnchorUin proto.Option[uint64] `protobuf:"varint,2,opt"`
	Name      []byte               `protobuf:"bytes,3,opt"`
}

type LiveChannelInfoFilter struct {
	IsNeedRoomId    proto.Option[uint32] `protobuf:"varint,1,opt"`
	IsNeedAnchorUin proto.Option[uint32] `protobuf:"varint,2,opt"`
	IsNeedName      proto.Option[uint32] `protobuf:"varint,3,opt"`
	_               [0]func()
}

type LiveRoomStatusChangeMsg struct {
	GuildId      proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChannelId    proto.Option[uint64] `protobuf:"varint,2,opt"`
	RoomId       proto.Option[uint64] `protobuf:"varint,3,opt"`
	AnchorTinyid proto.Option[uint64] `protobuf:"varint,4,opt"`
	Action       proto.Option[uint32] `protobuf:"varint,5,opt"`
	_            [0]func()
}

type MsgEvent struct {
	Seq          proto.Option[uint64] `protobuf:"varint,1,opt"`
	EventType    proto.Option[uint64] `protobuf:"varint,2,opt"`
	EventVersion proto.Option[uint64] `protobuf:"varint,3,opt"`
	_            [0]func()
}

type MsgSeq struct {
	Seq  proto.Option[uint64] `protobuf:"varint,1,opt"`
	Time proto.Option[uint64] `protobuf:"varint,2,opt"`
	_    [0]func()
}

type QuitGuild struct {
	_ [0]func()
}

type ReadNotify struct {
	ChannelId     proto.Option[uint64] `protobuf:"varint,1,opt"`
	GuildId       proto.Option[uint64] `protobuf:"varint,2,opt"`
	ReadMsgSeq    *MsgSeq              `protobuf:"bytes,3,opt"`
	ReadCntMsgSeq *MsgSeq              `protobuf:"bytes,4,opt"`
	ReadMsgMeta   []byte               `protobuf:"bytes,5,opt"`
}

type SchedulerMsg struct {
	CreatorHeadUrl []byte               `protobuf:"bytes,1,opt"`
	Wording        proto.Option[string] `protobuf:"bytes,2,opt"`
	ExpireTimeMs   proto.Option[uint64] `protobuf:"varint,3,opt"`
}

type SetAdmin struct {
	GuildId     proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChanId      proto.Option[uint64] `protobuf:"varint,2,opt"`
	OperatorId  proto.Option[uint64] `protobuf:"varint,3,opt"`
	AdminId     proto.Option[uint64] `protobuf:"varint,4,opt"`
	AdminTinyid proto.Option[uint64] `protobuf:"varint,5,opt"`
	OperateType proto.Option[uint32] `protobuf:"varint,6,opt"`
	_           [0]func()
}

type SetMsgRecvType struct {
	GuildId       proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChanId        proto.Option[uint64] `protobuf:"varint,2,opt"`
	OperatorId    proto.Option[uint64] `protobuf:"varint,3,opt"`
	MsgNotifyType proto.Option[uint32] `protobuf:"varint,4,opt"`
	_             [0]func()
}

type SetMute struct {
	Action proto.Option[uint32] `protobuf:"varint,1,opt"`
	TinyID proto.Option[uint64] `protobuf:"varint,2,opt"`
	_      [0]func()
}

type SetTop struct {
	Action proto.Option[uint32] `protobuf:"varint,1,opt"`
	_      [0]func()
}

type SwitchDetail struct {
	GuildId   proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChannelId proto.Option[uint64] `protobuf:"varint,2,opt"`
	Platform  proto.Option[uint32] `protobuf:"varint,3,opt"`
	_         [0]func()
}

type SwitchLiveRoom struct {
	GuildId   proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChannelId proto.Option[uint64] `protobuf:"varint,2,opt"`
	// optional uint64 roomId = 3;
	// optional uint64 tinyid = 4;
	UserInfo *SwitchLiveRoomUserInfo `protobuf:"bytes,3,opt"`
	Action   proto.Option[uint32]    `protobuf:"varint,4,opt"` // JOIN = 1 QUIT = 2
	_        [0]func()
}

type SwitchLiveRoomUserInfo struct {
	TinyId   proto.Option[uint64] `protobuf:"varint,1,opt"`
	Nickname proto.Option[string] `protobuf:"bytes,2,opt"`
	_        [0]func()
}

type SwitchVoiceChannel struct {
	MemberId    proto.Option[uint64] `protobuf:"varint,1,opt"`
	EnterDetail *SwitchDetail        `protobuf:"bytes,2,opt"`
	LeaveDetail *SwitchDetail        `protobuf:"bytes,3,opt"`
	_           [0]func()
}

type UpdateCategory struct {
	CategoryInfo           []*CategoryInfo `protobuf:"bytes,1,rep"`
	NoClassifyCategoryInfo *CategoryInfo   `protobuf:"bytes,2,opt"`
}

type UpdateMsg struct {
	MsgSeq             proto.Option[uint64] `protobuf:"varint,1,opt"`
	OrigMsgUncountable proto.Option[bool]   `protobuf:"varint,2,opt"`
	EventType          proto.Option[uint64] `protobuf:"varint,3,opt"`
	EventVersion       proto.Option[uint64] `protobuf:"varint,4,opt"`
	OperatorTinyid     proto.Option[uint64] `protobuf:"varint,5,opt"`
	OperatorRole       proto.Option[uint64] `protobuf:"varint,6,opt"`
	Reason             proto.Option[uint64] `protobuf:"varint,7,opt"`
	Timestamp          proto.Option[uint64] `protobuf:"varint,8,opt"`
	_                  [0]func()
}

type UpdateVoiceBlockList struct {
	Action       proto.Option[uint32] `protobuf:"varint,1,opt"`
	ObjectTinyid proto.Option[uint64] `protobuf:"varint,2,opt"`
	_            [0]func()
}

type VoiceChannelInfo struct {
	MemberMaxNum proto.Option[uint32] `protobuf:"varint,1,opt"`
	_            [0]func()
}

type VoiceChannelInfoFilter struct {
	MemberMaxNum proto.Option[uint32] `protobuf:"varint,1,opt"`
	_            [0]func()
}

type CommGrayTips_TemplParam struct {
	Name  []byte `protobuf:"bytes,1,opt"`
	Value []byte `protobuf:"bytes,2,opt"`
}
