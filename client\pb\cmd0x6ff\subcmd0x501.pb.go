// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/cmd0x6ff/subcmd0x501.proto

package cmd0x6ff

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type C501ReqBody struct {
	ReqBody *SubCmd0X501ReqBody `protobuf:"bytes,1281,opt"`
	_       [0]func()
}

type C501RspBody struct {
	RspBody *SubCmd0X501RspBody `protobuf:"bytes,1281,opt"`
	_       [0]func()
}

type SubCmd0X501ReqBody struct {
	Uin            proto.Option[uint64] `protobuf:"varint,1,opt"`
	IdcId          proto.Option[uint32] `protobuf:"varint,2,opt"`
	Appid          proto.Option[uint32] `protobuf:"varint,3,opt"`
	LoginSigType   proto.Option[uint32] `protobuf:"varint,4,opt"`
	LoginSigTicket []byte               `protobuf:"bytes,5,opt"`
	RequestFlag    proto.Option[uint32] `protobuf:"varint,6,opt"`
	ServiceTypes   []uint32             `protobuf:"varint,7,rep"`
	Bid            proto.Option[uint32] `protobuf:"varint,8,opt"`
}

type SubCmd0X501RspBody struct {
	SigSession []byte      `protobuf:"bytes,1,opt"`
	SessionKey []byte      `protobuf:"bytes,2,opt"`
	Addrs      []*SrvAddrs `protobuf:"bytes,3,rep"`
}

type SrvAddrs struct {
	ServiceType proto.Option[uint32] `protobuf:"varint,1,opt"`
	Addrs       []*IpAddr            `protobuf:"bytes,2,rep"`
}

type IpAddr struct {
	Type proto.Option[uint32] `protobuf:"varint,1,opt"`
	Ip   proto.Option[uint32] `protobuf:"fixed32,2,opt"`
	Port proto.Option[uint32] `protobuf:"varint,3,opt"`
	Area proto.Option[uint32] `protobuf:"varint,4,opt"`
	_    [0]func()
}
