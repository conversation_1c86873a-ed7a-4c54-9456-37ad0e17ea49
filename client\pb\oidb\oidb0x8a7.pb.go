// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0x8a7.proto

package oidb

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type D8A7ReqBody struct {
	SubCmd                    proto.Option[uint32] `protobuf:"varint,1,opt"`
	LimitIntervalTypeForUin   proto.Option[uint32] `protobuf:"varint,2,opt"`
	LimitIntervalTypeForGroup proto.Option[uint32] `protobuf:"varint,3,opt"`
	Uin                       proto.Option[uint64] `protobuf:"varint,4,opt"`
	GroupCode                 proto.Option[uint64] `protobuf:"varint,5,opt"`
	_                         [0]func()
}

type D8A7RspBody struct {
	CanAtAll                 proto.Option[bool]   `protobuf:"varint,1,opt"`
	RemainAtAllCountForUin   proto.Option[uint32] `protobuf:"varint,2,opt"`
	RemainAtAllCountForGroup proto.Option[uint32] `protobuf:"varint,3,opt"`
	PromptMsg1               []byte               `protobuf:"bytes,4,opt"`
	PromptMsg2               []byte               `protobuf:"bytes,5,opt"`
}
