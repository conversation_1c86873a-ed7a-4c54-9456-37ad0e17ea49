// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/channel/GuildFeedCloudRead.proto

package channel

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type GetNoticesReq struct {
	ExtInfo    *StCommonExt         `protobuf:"bytes,1,opt"`
	PageNum    proto.Option[uint32] `protobuf:"varint,2,opt"`
	AttachInfo proto.Option[string] `protobuf:"bytes,3,opt"`
	_          [0]func()
}

type GetNoticesRsp struct {
	ExtInfo    *StCommonExt         `protobuf:"bytes,1,opt"`
	Notices    []*StNotice          `protobuf:"bytes,2,rep"`
	TotalNum   proto.Option[uint32] `protobuf:"varint,3,opt"`
	IsFinish   proto.Option[bool]   `protobuf:"varint,4,opt"`
	AttachInfo proto.Option[string] `protobuf:"bytes,5,opt"`
}

type NeedInsertCommentInfo struct {
	CommentID proto.Option[string] `protobuf:"bytes,1,opt"`
	_         [0]func()
}

type RefreshToast struct {
	Text proto.Option[string] `protobuf:"bytes,1,opt"`
	_    [0]func()
}

type StGetChannelFeedsReq struct {
	ExtInfo       *StCommonExt         `protobuf:"bytes,1,opt"`
	Count         proto.Option[uint32] `protobuf:"varint,2,opt"`
	From          proto.Option[uint32] `protobuf:"varint,3,opt"`
	ChannelSign   *StChannelSign       `protobuf:"bytes,4,opt"`
	FeedAttchInfo proto.Option[string] `protobuf:"bytes,5,opt"`
	_             [0]func()
}

type StGetChannelFeedsRsp struct {
	ExtInfo       *StCommonExt         `protobuf:"bytes,1,opt"`
	VecFeed       []*StFeed            `protobuf:"bytes,2,rep"`
	IsFinish      proto.Option[uint32] `protobuf:"varint,3,opt"`
	User          *StUser              `protobuf:"bytes,4,opt"`
	FeedAttchInfo proto.Option[string] `protobuf:"bytes,5,opt"`
	RefreshToast  *RefreshToast        `protobuf:"bytes,6,opt"`
}

type StGetChannelShareFeedReq struct {
	ExtInfo          *StCommonExt         `protobuf:"bytes,1,opt"`
	From             proto.Option[uint32] `protobuf:"varint,2,opt"`
	ChannelShareInfo *StChannelShareInfo  `protobuf:"bytes,3,opt"`
	_                [0]func()
}

type StGetChannelShareFeedRsp struct {
	ExtInfo *StCommonExt `protobuf:"bytes,1,opt"`
	Feed    *StFeed      `protobuf:"bytes,2,opt"`
	_       [0]func()
}

type StGetFeedCommentsReq struct {
	ExtInfo     *StCommonExt         `protobuf:"bytes,1,opt"`
	UserId      proto.Option[string] `protobuf:"bytes,2,opt"`
	FeedId      proto.Option[string] `protobuf:"bytes,3,opt"`
	ListNum     proto.Option[uint32] `protobuf:"varint,4,opt"`
	From        proto.Option[uint32] `protobuf:"varint,5,opt"`
	AttchInfo   proto.Option[string] `protobuf:"bytes,6,opt"`
	EntrySchema proto.Option[string] `protobuf:"bytes,7,opt"`
	_           [0]func()
}

type StGetFeedCommentsRsp struct {
	ExtInfo    *StCommonExt         `protobuf:"bytes,1,opt"`
	VecComment []*StComment         `protobuf:"bytes,2,rep"`
	TotalNum   proto.Option[uint32] `protobuf:"varint,3,opt"`
	IsFinish   proto.Option[uint32] `protobuf:"varint,4,opt"`
	AttchInfo  proto.Option[string] `protobuf:"bytes,5,opt"`
}

type StGetFeedDetailReq struct {
	ExtInfo     *StCommonExt         `protobuf:"bytes,1,opt"`
	From        proto.Option[uint32] `protobuf:"varint,2,opt"`
	UserId      proto.Option[string] `protobuf:"bytes,3,opt"`
	FeedId      proto.Option[string] `protobuf:"bytes,4,opt"`
	CreateTime  proto.Option[uint64] `protobuf:"varint,5,opt"`
	DetailType  proto.Option[uint32] `protobuf:"varint,6,opt"`
	ChannelSign *StChannelSign       `protobuf:"bytes,7,opt"`
	_           [0]func()
}

type StGetFeedDetailRsp struct {
	ExtInfo   *StCommonExt `protobuf:"bytes,1,opt"`
	Feed      *StFeed      `protobuf:"bytes,2,opt"`
	LoginUser *StUser      `protobuf:"bytes,3,opt"`
	_         [0]func()
}
