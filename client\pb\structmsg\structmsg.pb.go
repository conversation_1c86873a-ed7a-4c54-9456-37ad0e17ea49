// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/structmsg/structmsg.proto

package structmsg

type AddFrdSNInfo struct {
	NotSeeDynamic int32 `protobuf:"varint,1,opt"`
	SetSn         int32 `protobuf:"varint,2,opt"`
	_             [0]func()
}

type FlagInfo struct {
	GrpMsgKickAdmin                   int32 `protobuf:"varint,1,opt"`
	GrpMsgHiddenGrp                   int32 `protobuf:"varint,2,opt"`
	GrpMsgWordingDown                 int32 `protobuf:"varint,3,opt"`
	FrdMsgGetBusiCard                 int32 `protobuf:"varint,4,opt"`
	GrpMsgGetOfficialAccount          int32 `protobuf:"varint,5,opt"`
	GrpMsgGetPayInGroup               int32 `protobuf:"varint,6,opt"`
	FrdMsgDiscuss2ManyChat            int32 `protobuf:"varint,7,opt"`
	GrpMsgNotAllowJoinGrpInviteNotFrd int32 `protobuf:"varint,8,opt"`
	FrdMsgNeedWaitingMsg              int32 `protobuf:"varint,9,opt"`
	FrdMsgUint32NeedAllUnreadMsg      int32 `protobuf:"varint,10,opt"`
	GrpMsgNeedAutoAdminWording        int32 `protobuf:"varint,11,opt"`
	GrpMsgGetTransferGroupMsgFlag     int32 `protobuf:"varint,12,opt"`
	GrpMsgGetQuitPayGroupMsgFlag      int32 `protobuf:"varint,13,opt"`
	GrpMsgSupportInviteAutoJoin       int32 `protobuf:"varint,14,opt"`
	GrpMsgMaskInviteAutoJoin          int32 `protobuf:"varint,15,opt"`
	GrpMsgGetDisbandedByAdmin         int32 `protobuf:"varint,16,opt"`
	GrpMsgGetC2CInviteJoinGroup       int32 `protobuf:"varint,17,opt"`
	_                                 [0]func()
}

type FriendInfo struct {
	MsgJointFriend string `protobuf:"bytes,1,opt"`
	MsgBlacklist   string `protobuf:"bytes,2,opt"`
	_              [0]func()
}

type SGroupInfo struct {
	GroupAuthType     int32  `protobuf:"varint,1,opt"`
	DisplayAction     int32  `protobuf:"varint,2,opt"`
	MsgAlert          string `protobuf:"bytes,3,opt"`
	MsgDetailAlert    string `protobuf:"bytes,4,opt"`
	MsgOtherAdminDone string `protobuf:"bytes,5,opt"`
	AppPrivilegeFlag  int32  `protobuf:"varint,6,opt"`
	_                 [0]func()
}

type MsgInviteExt struct {
	SrcType   int32 `protobuf:"varint,1,opt"`
	SrcCode   int64 `protobuf:"varint,2,opt"`
	WaitState int32 `protobuf:"varint,3,opt"`
	_         [0]func()
}

type MsgPayGroupExt struct {
	JoinGrpTime int64 `protobuf:"varint,1,opt"`
	QuitGrpTime int64 `protobuf:"varint,2,opt"`
	_           [0]func()
}

type ReqNextSystemMsg struct {
	MsgNum             int32     `protobuf:"varint,1,opt"`
	FollowingFriendSeq int64     `protobuf:"varint,2,opt"`
	FollowingGroupSeq  int64     `protobuf:"varint,3,opt"`
	Checktype          int32     `protobuf:"varint,4,opt"`
	Flag               *FlagInfo `protobuf:"bytes,5,opt"`
	Language           int32     `protobuf:"varint,6,opt"`
	Version            int32     `protobuf:"varint,7,opt"`
	FriendMsgTypeFlag  int64     `protobuf:"varint,8,opt"`
	_                  [0]func()
}

type ReqSystemMsg struct {
	MsgNum          int32 `protobuf:"varint,1,opt"`
	LatestFriendSeq int64 `protobuf:"varint,2,opt"`
	LatestGroupSeq  int64 `protobuf:"varint,3,opt"`
	Version         int32 `protobuf:"varint,4,opt"`
	Language        int32 `protobuf:"varint,5,opt"`
	_               [0]func()
}

type ReqSystemMsgAction struct {
	MsgType      int32                `protobuf:"varint,1,opt"`
	MsgSeq       int64                `protobuf:"varint,2,opt"`
	ReqUin       int64                `protobuf:"varint,3,opt"`
	SubType      int32                `protobuf:"varint,4,opt"`
	SrcId        int32                `protobuf:"varint,5,opt"`
	SubSrcId     int32                `protobuf:"varint,6,opt"`
	GroupMsgType int32                `protobuf:"varint,7,opt"`
	ActionInfo   *SystemMsgActionInfo `protobuf:"bytes,8,opt"`
	Language     int32                `protobuf:"varint,9,opt"`
	_            [0]func()
}

type ReqSystemMsgNew struct {
	MsgNum            int32     `protobuf:"varint,1,opt"`
	LatestFriendSeq   int64     `protobuf:"varint,2,opt"`
	LatestGroupSeq    int64     `protobuf:"varint,3,opt"`
	Version           int32     `protobuf:"varint,4,opt"`
	Checktype         int32     `protobuf:"varint,5,opt"`
	Flag              *FlagInfo `protobuf:"bytes,6,opt"`
	Language          int32     `protobuf:"varint,7,opt"`
	IsGetFrdRibbon    bool      `protobuf:"varint,8,opt"`
	IsGetGrpRibbon    bool      `protobuf:"varint,9,opt"`
	FriendMsgTypeFlag int64     `protobuf:"varint,10,opt"`
	ReqMsgType        int32     `protobuf:"varint,11,opt"`
	_                 [0]func()
}

type ReqSystemMsgRead struct {
	LatestFriendSeq int64 `protobuf:"varint,1,opt"`
	LatestGroupSeq  int64 `protobuf:"varint,2,opt"`
	Type            int32 `protobuf:"varint,3,opt"`
	Checktype       int32 `protobuf:"varint,4,opt"`
	_               [0]func()
}

type RspHead struct {
	Result  int32  `protobuf:"varint,1,opt"`
	MsgFail string `protobuf:"bytes,2,opt"`
	_       [0]func()
}

type RspNextSystemMsg struct {
	Head               *RspHead     `protobuf:"bytes,1,opt"`
	Msgs               []*StructMsg `protobuf:"bytes,2,rep"`
	FollowingFriendSeq int64        `protobuf:"varint,3,opt"`
	FollowingGroupSeq  int64        `protobuf:"varint,4,opt"`
	Checktype          int32        `protobuf:"varint,5,opt"`
	GameNick           string       `protobuf:"bytes,100,opt"`
	UndecidForQim      []byte       `protobuf:"bytes,101,opt"`
	UnReadCount3       int32        `protobuf:"varint,102,opt"`
}

type RspSystemMsg struct {
	Head               *RspHead     `protobuf:"bytes,1,opt"`
	Msgs               []*StructMsg `protobuf:"bytes,2,rep"`
	UnreadCount        int32        `protobuf:"varint,3,opt"`
	LatestFriendSeq    int64        `protobuf:"varint,4,opt"`
	LatestGroupSeq     int64        `protobuf:"varint,5,opt"`
	FollowingFriendSeq int64        `protobuf:"varint,6,opt"`
	FollowingGroupSeq  int64        `protobuf:"varint,7,opt"`
	MsgDisplay         string       `protobuf:"bytes,8,opt"`
}

type RspSystemMsgAction struct {
	Head              *RspHead `protobuf:"bytes,1,opt"`
	MsgDetail         string   `protobuf:"bytes,2,opt"`
	Type              int32    `protobuf:"varint,3,opt"`
	MsgInvalidDecided string   `protobuf:"bytes,5,opt"`
	RemarkResult      int32    `protobuf:"varint,6,opt"`
	_                 [0]func()
}

type RspSystemMsgNew struct {
	Head               *RspHead     `protobuf:"bytes,1,opt"`
	UnreadFriendCount  int32        `protobuf:"varint,2,opt"`
	UnreadGroupCount   int32        `protobuf:"varint,3,opt"`
	LatestFriendSeq    int64        `protobuf:"varint,4,opt"`
	LatestGroupSeq     int64        `protobuf:"varint,5,opt"`
	FollowingFriendSeq int64        `protobuf:"varint,6,opt"`
	FollowingGroupSeq  int64        `protobuf:"varint,7,opt"`
	Friendmsgs         []*StructMsg `protobuf:"bytes,9,rep"`
	Groupmsgs          []*StructMsg `protobuf:"bytes,10,rep"`
	MsgRibbonFriend    *StructMsg   `protobuf:"bytes,11,opt"`
	MsgRibbonGroup     *StructMsg   `protobuf:"bytes,12,opt"`
	MsgDisplay         string       `protobuf:"bytes,13,opt"`
	GrpMsgDisplay      string       `protobuf:"bytes,14,opt"`
	Over               int32        `protobuf:"varint,15,opt"`
	Checktype          int32        `protobuf:"varint,20,opt"`
	GameNick           string       `protobuf:"bytes,100,opt"`
	UndecidForQim      []byte       `protobuf:"bytes,101,opt"`
	UnReadCount3       int32        `protobuf:"varint,102,opt"`
}

type RspSystemMsgRead struct {
	Head      *RspHead `protobuf:"bytes,1,opt"`
	Type      int32    `protobuf:"varint,2,opt"`
	Checktype int32    `protobuf:"varint,3,opt"`
	_         [0]func()
}

type StructMsg struct {
	Version    int32      `protobuf:"varint,1,opt"`
	MsgType    int32      `protobuf:"varint,2,opt"`
	MsgSeq     int64      `protobuf:"varint,3,opt"`
	MsgTime    int64      `protobuf:"varint,4,opt"`
	ReqUin     int64      `protobuf:"varint,5,opt"`
	UnreadFlag int32      `protobuf:"varint,6,opt"`
	Msg        *SystemMsg `protobuf:"bytes,50,opt"`
	_          [0]func()
}

type SystemMsg struct {
	SubType       int32  `protobuf:"varint,1,opt"`
	MsgTitle      string `protobuf:"bytes,2,opt"`
	MsgDescribe   string `protobuf:"bytes,3,opt"`
	MsgAdditional string `protobuf:"bytes,4,opt"`
	MsgSource     string `protobuf:"bytes,5,opt"`
	MsgDecided    string `protobuf:"bytes,6,opt"`
	SrcId         int32  `protobuf:"varint,7,opt"`
	SubSrcId      int32  `protobuf:"varint,8,opt"`
	// repeated SystemMsgAction actions = 9;
	GroupCode        int64 `protobuf:"varint,10,opt"`
	ActionUin        int64 `protobuf:"varint,11,opt"`
	GroupMsgType     int32 `protobuf:"varint,12,opt"`
	GroupInviterRole int32 `protobuf:"varint,13,opt"`
	// FriendInfo friendInfo = 14;
	// SGroupInfo groupInfo = 15;
	ActorUin          int64  `protobuf:"varint,16,opt"`
	MsgActorDescribe  string `protobuf:"bytes,17,opt"`
	MsgAdditionalList string `protobuf:"bytes,18,opt"`
	Relation          int32  `protobuf:"varint,19,opt"`
	Reqsubtype        int32  `protobuf:"varint,20,opt"`
	CloneUin          int64  `protobuf:"varint,21,opt"`
	DiscussUin        int64  `protobuf:"varint,22,opt"`
	EimGroupId        int64  `protobuf:"varint,23,opt"`
	// MsgInviteExt msgInviteExtinfo = 24;
	// MsgPayGroupExt msgPayGroupExtinfo = 25;
	SourceFlag             int32  `protobuf:"varint,26,opt"`
	GameNick               []byte `protobuf:"bytes,27,opt"`
	GameMsg                []byte `protobuf:"bytes,28,opt"`
	GroupFlagext3          int32  `protobuf:"varint,29,opt"`
	GroupOwnerUin          int64  `protobuf:"varint,30,opt"`
	DoubtFlag              int32  `protobuf:"varint,31,opt"`
	WarningTips            []byte `protobuf:"bytes,32,opt"`
	NameMore               []byte `protobuf:"bytes,33,opt"`
	ReqUinFaceid           int32  `protobuf:"varint,50,opt"`
	ReqUinNick             string `protobuf:"bytes,51,opt"`
	GroupName              string `protobuf:"bytes,52,opt"`
	ActionUinNick          string `protobuf:"bytes,53,opt"`
	MsgQna                 string `protobuf:"bytes,54,opt"`
	MsgDetail              string `protobuf:"bytes,55,opt"`
	GroupExtFlag           int32  `protobuf:"varint,57,opt"`
	ActorUinNick           string `protobuf:"bytes,58,opt"`
	PicUrl                 string `protobuf:"bytes,59,opt"`
	CloneUinNick           string `protobuf:"bytes,60,opt"`
	ReqUinBusinessCard     string `protobuf:"bytes,61,opt"`
	EimGroupIdName         string `protobuf:"bytes,63,opt"`
	ReqUinPreRemark        string `protobuf:"bytes,64,opt"`
	ActionUinQqNick        string `protobuf:"bytes,65,opt"`
	ActionUinRemark        string `protobuf:"bytes,66,opt"`
	ReqUinGender           int32  `protobuf:"varint,67,opt"`
	ReqUinAge              int32  `protobuf:"varint,68,opt"`
	C2CInviteJoinGroupFlag int32  `protobuf:"varint,69,opt"`
	CardSwitch             int32  `protobuf:"varint,101,opt"`
}

type SystemMsgAction struct {
	Name       string               `protobuf:"bytes,1,opt"`
	Result     string               `protobuf:"bytes,2,opt"`
	Action     int32                `protobuf:"varint,3,opt"`
	ActionInfo *SystemMsgActionInfo `protobuf:"bytes,4,opt"`
	DetailName string               `protobuf:"bytes,5,opt"`
	_          [0]func()
}

type SystemMsgActionInfo struct {
	Type         int32         `protobuf:"varint,1,opt"`
	GroupCode    int64         `protobuf:"varint,2,opt"`
	Sig          []byte        `protobuf:"bytes,3,opt"`
	Msg          string        `protobuf:"bytes,50,opt"`
	GroupId      int32         `protobuf:"varint,51,opt"`
	Remark       string        `protobuf:"bytes,52,opt"`
	Blacklist    bool          `protobuf:"varint,53,opt"`
	AddFrdSNInfo *AddFrdSNInfo `protobuf:"bytes,54,opt"`
}
