# QQ机器人Python实现指南

## 环境准备

### 1. 下载go-cqhttp
```bash
# 访问 https://github.com/Mrs4s/go-cqhttp/releases
# 下载适合你系统的版本，例如：go-cqhttp_windows_amd64.exe
```

### 2. 配置go-cqhttp
创建 `config.yml` 文件：
```yaml
account:
  uin: 你的QQ号
  password: '你的QQ密码'
  encrypt: false
  status: 0
  relogin:
    delay: 3
    count: 3
    interval: 3

heartbeat:
  interval: 5

message:
  post-format: string
  ignore-invalid-cqcode: false
  force-fragment: false
  fix-url: false
  proxy-rewrite: ''
  report-self-message: false
  remove-reply-at: false
  extra-reply-data: false
  skip-mime-scan: false

output:
  log-level: warn
  log-aging: 15
  log-force-new: true
  log-colorful: true
  debug: false

default-middlewares: &default
  access-token: ''
  filter: ''
  rate-limit:
    enabled: false
    frequency: 1
    bucket: 1

database:
  leveldb:
    enable: true

servers:
  - http:
      host: 127.0.0.1
      port: 5700
      timeout: 5
      middlewares:
        <<: *default
      post:
        - url: 'http://127.0.0.1:8080/'
          secret: ''
```

### 3. 安装Python环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install nonebot2[fastapi]
pip install nonebot-adapter-onebot
```

## 项目结构
```
qq_bot/
├── bot.py              # 主程序
├── .env                # 环境配置
├── requirements.txt    # Python依赖
├── start.bat          # Windows启动脚本
├── start.sh           # Linux/Mac启动脚本
├── plugins/            # 插件目录
│   ├── __init__.py
│   └── keyword_reply.py # 关键词回复插件
└── data/
    └── keywords.json   # 关键词数据（自动生成）
```

## 快速开始

### 1. 设置go-cqhttp
1. 下载go-cqhttp到项目目录
2. 创建config.yml配置文件（见上面配置）
3. 修改配置中的QQ号和密码
4. 运行go-cqhttp，完成登录验证

### 2. 设置Python机器人
```bash
# 安装依赖
pip install -r requirements.txt

# 修改.env文件中的管理员QQ号
# SUPERUSERS=["你的QQ号"]

# 启动机器人
python bot.py
# 或者使用启动脚本
# Windows: start.bat
# Linux/Mac: chmod +x start.sh && ./start.sh
```

## 功能说明

### 关键词匹配类型

1. **精确匹配 (exact)**
   - 消息内容完全匹配关键词
   - 示例：用户发送"你好"，机器人回复"你好！"

2. **模糊匹配 (fuzzy)**
   - 消息内容包含关键词即可触发
   - 示例：用户发送"今天天气怎么样"，包含"天气"关键词，触发回复

3. **正则匹配 (regex)**
   - 使用正则表达式匹配
   - 支持动态内容，如计算、时间查询等

### 管理命令（仅管理员可用）

```bash
# 添加关键词
/添加关键词 exact 你好 你好！ Hi！ 欢迎！

# 删除关键词
/删除关键词 exact 你好

# 查看所有关键词
/关键词列表
```

### 默认关键词示例

- **精确匹配**：
  - "你好" → 随机回复："你好！"、"Hi！"、"欢迎！"
  - "再见" → 随机回复："再见！"、"拜拜！"、"下次见！"
  - "谢谢" → 随机回复："不客气！"、"不用谢！"、"很高兴帮到你！"

- **模糊匹配**：
  - 包含"天气" → 随机回复："今天天气不错呢！"、"记得关注天气变化哦！"
  - 包含"吃饭" → 随机回复："记得按时吃饭！"、"要好好吃饭哦！"
  - 包含"学习" → 随机回复："学习使人进步！"、"加油学习！"、"知识就是力量！"

- **正则匹配**：
  - "数字+数字" → 自动计算结果
  - 包含"时间"或"几点" → 返回当前时间

## 自定义扩展

### 添加新的匹配规则
编辑 `plugins/keyword_reply.py` 文件中的 `match_message` 方法，可以添加更复杂的匹配逻辑。

### 添加新功能
在 `plugins/` 目录下创建新的插件文件，NoneBot2会自动加载。

## 注意事项

1. 确保go-cqhttp和Python机器人都在运行
2. 检查端口配置是否一致（默认5700和8080）
3. 管理员命令只有SUPERUSERS中的用户可以使用
4. 关键词数据保存在data/keywords.json文件中
5. 支持群聊和私聊消息

## 故障排除

### 常见问题
1. **机器人无响应**：检查go-cqhttp是否正常运行，查看日志
2. **权限错误**：确认.env文件中的SUPERUSERS配置正确
3. **端口冲突**：修改.env中的PORT或config.yml中的端口
4. **登录失败**：检查QQ号密码，可能需要设备验证

### 日志查看
- go-cqhttp日志：查看go-cqhttp运行窗口
- Python机器人日志：查看Python运行窗口
