syntax = "proto2";

package channel;

option go_package = "github.com/Mrs4s/MiraiGo/client/pb/channel";

message AppChannelMsg {
  optional string summary = 1;
  optional string msg = 2;
  optional uint64 expireTimeMs = 3;
  optional uint32 schemaType = 4;
  optional string schema = 5;
}

message CategoryChannelInfo {
  optional uint32 channelIndex = 1;
  optional uint64 channelId = 2;
}

message CategoryInfo {
  optional uint32 categoryIndex = 1;
  repeated CategoryChannelInfo channelInfo = 2;
  optional bytes categoryName = 3;
  optional uint64 categoryId = 4;
}

message ChanInfoFilter {
  optional uint32 channelName = 2;
  optional uint32 creatorId = 3;
  optional uint32 createTime = 4;
  optional uint32 guildId = 5;
  optional uint32 msgNotifyType = 6;
  optional uint32 channelType = 7;
  optional uint32 speakPermission = 8;
  optional uint32 lastMsgSeq = 11;
  optional uint32 lastCntMsgSeq = 12;
  optional VoiceChannelInfoFilter voiceChannelInfoFilter = 14;
  optional LiveChannelInfoFilter liveChannelInfoFilter = 15;
  optional uint32 bannedSpeak = 16;
}

message ChangeChanInfo {
  optional uint64 guildId = 1;
  optional uint64 chanId = 2;
  optional uint64 operatorId = 3;
  optional MsgSeq infoSeq = 4;
  optional uint32 updateType = 5;
  optional ChanInfoFilter chanInfoFilter = 6;
  optional ServChannelInfo chanInfo = 7;
}

message ChangeGuildInfo {
  optional uint64 guildId = 1;
  optional uint64 operatorId = 2;
  optional MsgSeq infoSeq = 3;
  optional MsgSeq faceSeq = 4;
  optional uint32 updateType = 5;
  optional GuildInfoFilter guildInfoFilter = 6;
  optional GuildInfo guildInfo = 7;
}

message ChannelID {
  optional uint64 chanId = 1;
}

message ServChannelInfo {
  optional uint64 channelId = 1;
  optional bytes channelName = 2;
  optional uint64 creatorId = 3;
  optional uint64 createTime = 4;
  optional uint64 guildId = 5;
  optional uint32 msgNotifyType = 6;
  optional uint32 channelType = 7;
  optional uint32 speakPermission = 8;
  optional MsgSeq lastMsgSeq = 11;
  optional MsgSeq lastCntMsgSeq = 12;
  optional VoiceChannelInfo voiceChannelInfo = 14;
  optional LiveChannelInfo liveChannelInfo = 15;
  optional uint32 bannedSpeak = 16;
}

message CommGrayTips {
  optional uint64 busiType = 1;
  optional uint64 busiId = 2;
  optional uint32 ctrlFlag = 3;
  optional uint64 templId = 4;
  repeated TemplParam templParam = 5;
  optional bytes content = 6;
  optional uint64 tipsSeqId = 10;
  optional bytes pbReserv = 100;

  message TemplParam {
    optional bytes name = 1;
    optional bytes value = 2;
  }
}

message CreateChan {
  optional uint64 guildId = 1;
  optional uint64 operatorId = 3;
  repeated ChannelID createId = 4;
}

message CreateGuild {
  optional uint64 operatorId = 1;
  optional uint64 guildId = 2;
}

message DestroyChan {
  optional uint64 guildId = 1;
  optional uint64 operatorId = 3;
  repeated ChannelID deleteId = 4;
}

message DestroyGuild {
  optional uint64 operatorId = 1;
  optional uint64 guildId = 2;
}

message EventBody {
  optional ReadNotify readNotify = 1;
  optional CommGrayTips commGrayTips = 2;
  optional CreateGuild createGuild = 3;
  optional DestroyGuild destroyGuild = 4;
  optional JoinGuild joinGuild = 5;
  optional KickOffGuild kickOffGuild = 6;
  optional QuitGuild quitGuild = 7;
  optional ChangeGuildInfo changeGuildInfo = 8;
  optional CreateChan createChan = 9;
  optional DestroyChan destroyChan = 10;
  optional ChangeChanInfo changeChanInfo = 11;
  optional SetAdmin setAdmin = 12;
  optional SetMsgRecvType setMsgRecvType = 13;
  optional UpdateMsg updateMsg = 14;
  optional SetTop setTop = 17;
  optional SwitchVoiceChannel switchChannel = 18;
  optional UpdateCategory updateCategory = 21;
  optional UpdateVoiceBlockList updateVoiceBlockList = 22;
  optional SetMute setMute = 23;
  optional LiveRoomStatusChangeMsg liveStatusChangeRoom = 24;
  optional SwitchLiveRoom switchLiveRoom = 25;
  repeated MsgEvent events = 39;
  optional SchedulerMsg scheduler = 40;
  optional AppChannelMsg appChannel = 41;
  optional FeedEvent feedEvent = 44;
  optional AppChannelMsg weakMsgAppChannel = 46;
  optional ReadFeedNotify readFeedNotify = 48;
}

message FeedEvent {
  optional uint64 guildId = 1;
  optional uint64 channelId = 2;
  optional string feedId = 3;
  optional string msgSummary = 4;
  optional uint64 eventTime = 5;
}

message ReadFeedNotify {
  optional uint64 reportTime = 2;
}

message GroupProStatus {
  optional uint32 isEnable = 1;
  optional uint32 isBanned = 2;
  optional uint32 isFrozen = 3;
}

message GuildInfo {
  optional uint64 guildCode = 2;
  optional uint64 ownerId = 3;
  optional uint64 createTime = 4;
  optional uint32 memberMaxNum = 5;
  optional uint32 memberNum = 6;
  optional uint32 guildType = 7;
  optional bytes guildName = 8;
  repeated uint64 robotList = 9;
  repeated uint64 adminList = 10;
  optional uint32 robotMaxNum = 11;
  optional uint32 adminMaxNum = 12;
  optional bytes profile = 13;
  optional uint64 faceSeq = 14;
  optional GroupProStatus guildStatus = 15;
  optional uint32 channelNum = 16;
  optional MsgSeq memberChangeSeq = 5002;
  optional MsgSeq guildInfoChangeSeq = 5003;
  optional MsgSeq channelChangeSeq = 5004;
}

message GuildInfoFilter {
  optional uint32 guildCode = 2;
  optional uint32 ownerId = 3;
  optional uint32 createTime = 4;
  optional uint32 memberMaxNum = 5;
  optional uint32 memberNum = 6;
  optional uint32 guildType = 7;
  optional uint32 guildName = 8;
  optional uint32 robotList = 9;
  optional uint32 adminList = 10;
  optional uint32 robotMaxNum = 11;
  optional uint32 adminMaxNum = 12;
  optional uint32 profile = 13;
  optional uint32 faceSeq = 14;
  optional uint32 guildStatus = 15;
  optional uint32 channelNum = 16;
  optional uint32 memberChangeSeq = 5002;
  optional uint32 guildInfoChangeSeq = 5003;
  optional uint32 channelChangeSeq = 5004;
}

message JoinGuild {
  optional uint64 memberId = 3;
  optional uint32 memberType = 4;
  optional uint64 memberTinyid = 5;
}

message KickOffGuild {
  optional uint64 memberId = 3;
  optional uint32 setBlack = 4;
  optional uint64 memberTinyid = 5;
}

message LiveChannelInfo {
  optional uint64 roomId = 1;
  optional uint64 anchorUin = 2;
  optional bytes name = 3;
}

message LiveChannelInfoFilter {
  optional uint32 isNeedRoomId = 1;
  optional uint32 isNeedAnchorUin = 2;
  optional uint32 isNeedName = 3;
}

message LiveRoomStatusChangeMsg {
  optional uint64 guildId = 1;
  optional uint64 channelId = 2;
  optional uint64 roomId = 3;
  optional uint64 anchorTinyid = 4;
  optional uint32 action = 5;
}

message MsgEvent {
  optional uint64 seq = 1;
  optional uint64 eventType = 2;
  optional uint64 eventVersion = 3;
}

message MsgSeq {
  optional uint64 seq = 1;
  optional uint64 time = 2;
}

message QuitGuild {}

message ReadNotify {
  optional uint64 channelId = 1;
  optional uint64 guildId = 2;
  optional MsgSeq readMsgSeq = 3;
  optional MsgSeq readCntMsgSeq = 4;
  optional bytes readMsgMeta = 5;
}

message SchedulerMsg {
  optional bytes creatorHeadUrl = 1;
  optional string wording = 2;
  optional uint64 expireTimeMs = 3;
}

message SetAdmin {
  optional uint64 guildId = 1;
  optional uint64 chanId = 2;
  optional uint64 operatorId = 3;
  optional uint64 adminId = 4;
  optional uint64 adminTinyid = 5;
  optional uint32 operateType = 6;
}

message SetMsgRecvType {
  optional uint64 guildId = 1;
  optional uint64 chanId = 2;
  optional uint64 operatorId = 3;
  optional uint32 msgNotifyType = 4;
}

message SetMute {
  optional uint32 action = 1;
  optional uint64 tinyID = 2;
}

message SetTop {
  optional uint32 action = 1;
}

message SwitchDetail {
  optional uint64 guildId = 1;
  optional uint64 channelId = 2;
  optional uint32 platform = 3;
}

message SwitchLiveRoom {
  optional uint64 guildId = 1;
  optional uint64 channelId = 2;
  // optional uint64 roomId = 3;
  // optional uint64 tinyid = 4;
  optional SwitchLiveRoomUserInfo userInfo = 3;
  optional uint32 action = 4; // JOIN = 1 QUIT = 2
}

message SwitchLiveRoomUserInfo {
  optional uint64 tinyId = 1;
  optional string nickname = 2;
}

message SwitchVoiceChannel {
  optional uint64 memberId = 1;
  optional SwitchDetail enterDetail = 2;
  optional SwitchDetail leaveDetail = 3;
}

message UpdateCategory {
  repeated CategoryInfo categoryInfo = 1;
  optional CategoryInfo noClassifyCategoryInfo = 2;
}

message UpdateMsg {
  optional uint64 msgSeq = 1;
  optional bool origMsgUncountable = 2;
  optional uint64 eventType = 3;
  optional uint64 eventVersion = 4;
  optional uint64 operatorTinyid = 5;
  optional uint64 operatorRole = 6;
  optional uint64 reason = 7;
  optional uint64 timestamp = 8;
}

message UpdateVoiceBlockList {
  optional uint32 action = 1;
  optional uint64 objectTinyid = 2;
}

message VoiceChannelInfo {
  optional uint32 memberMaxNum = 1;
}

message VoiceChannelInfoFilter {
  optional uint32 memberMaxNum = 1;
}
