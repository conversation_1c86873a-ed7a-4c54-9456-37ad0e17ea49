syntax = "proto2";

option go_package = "github.com/Mrs4s/MiraiGo/client/pb/msg";

message ExtChannelInfo {
  optional uint64 guildId = 1;
  optional uint64 channelId = 2;
}

message TextResvAttr {
  optional bytes wording = 1;
  optional uint32 textAnalysisResult = 2;
  optional uint32 atType = 3;
  optional uint64 atMemberUin = 4;
  optional uint64 atMemberTinyid = 5;
  //optional ExtRoleInfo atMemberRoleInfo = 6;
  //optional ExtRoleInfo atRoleInfo = 7;
  optional ExtChannelInfo atChannelInfo = 8;
}

message ExtRoleInfo {
  optional uint64 id = 1;
  optional bytes info = 2;
  optional uint32 flag = 3;
}