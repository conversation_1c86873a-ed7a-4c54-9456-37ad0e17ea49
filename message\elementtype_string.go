// Code generated by "stringer -type ElementType -linecomment"; DO NOT EDIT.

package message

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[Text-0]
	_ = x[Image-1]
	_ = x[Face-2]
	_ = x[At-3]
	_ = x[Reply-4]
	_ = x[Service-5]
	_ = x[Forward-6]
	_ = x[File-7]
	_ = x[Voice-8]
	_ = x[Video-9]
	_ = x[LightApp-10]
	_ = x[RedBag-11]
}

const _ElementType_name = "文本图片表情艾特回复服务转发文件语音视频轻应用红包"

var _ElementType_index = [...]uint8{0, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 69, 75}

func (i ElementType) String() string {
	if i < 0 || i >= ElementType(len(_ElementType_index)-1) {
		return "ElementType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _ElementType_name[_ElementType_index[i]:_ElementType_index[i+1]]
}
