// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/multimsg/multimsg.proto

package multimsg

type ExternMsg struct {
	ChannelType int32 `protobuf:"varint,1,opt"`
	_           [0]func()
}

type MultiMsgApplyDownReq struct {
	MsgResid []byte `protobuf:"bytes,1,opt"`
	MsgType  int32  `protobuf:"varint,2,opt"`
	SrcUin   int64  `protobuf:"varint,3,opt"`
}

type MultiMsgApplyDownRsp struct {
	Result           int32      `protobuf:"varint,1,opt"`
	ThumbDownPara    []byte     `protobuf:"bytes,2,opt"`
	MsgKey           []byte     `protobuf:"bytes,3,opt"`
	Uint32DownIp     []int32    `protobuf:"varint,4,rep"`
	Uint32DownPort   []int32    `protobuf:"varint,5,rep"`
	MsgResid         []byte     `protobuf:"bytes,6,opt"`
	MsgExternInfo    *ExternMsg `protobuf:"bytes,7,opt"`
	BytesDownIpV6    [][]byte   `protobuf:"bytes,8,rep"`
	Uint32DownV6Port []int32    `protobuf:"varint,9,rep"`
}

type MultiMsgApplyUpReq struct {
	DstUin  int64  `protobuf:"varint,1,opt"`
	MsgSize int64  `protobuf:"varint,2,opt"`
	MsgMd5  []byte `protobuf:"bytes,3,opt"`
	MsgType int32  `protobuf:"varint,4,opt"`
	ApplyId int32  `protobuf:"varint,5,opt"`
}

type MultiMsgApplyUpRsp struct {
	Result         int32      `protobuf:"varint,1,opt"`
	MsgResid       string     `protobuf:"bytes,2,opt"`
	MsgUkey        []byte     `protobuf:"bytes,3,opt"`
	Uint32UpIp     []int32    `protobuf:"varint,4,rep"`
	Uint32UpPort   []int32    `protobuf:"varint,5,rep"`
	BlockSize      int64      `protobuf:"varint,6,opt"`
	UpOffset       int64      `protobuf:"varint,7,opt"`
	ApplyId        int32      `protobuf:"varint,8,opt"`
	MsgKey         []byte     `protobuf:"bytes,9,opt"`
	MsgSig         []byte     `protobuf:"bytes,10,opt"`
	MsgExternInfo  *ExternMsg `protobuf:"bytes,11,opt"`
	BytesUpIpV6    [][]byte   `protobuf:"bytes,12,rep"`
	Uint32UpV6Port []int32    `protobuf:"varint,13,rep"`
}

type MultiReqBody struct {
	Subcmd               int32                   `protobuf:"varint,1,opt"`
	TermType             int32                   `protobuf:"varint,2,opt"`
	PlatformType         int32                   `protobuf:"varint,3,opt"`
	NetType              int32                   `protobuf:"varint,4,opt"`
	BuildVer             string                  `protobuf:"bytes,5,opt"`
	MultimsgApplyupReq   []*MultiMsgApplyUpReq   `protobuf:"bytes,6,rep"`
	MultimsgApplydownReq []*MultiMsgApplyDownReq `protobuf:"bytes,7,rep"`
	BuType               int32                   `protobuf:"varint,8,opt"`
	ReqChannelType       int32                   `protobuf:"varint,9,opt"`
}

type MultiRspBody struct {
	Subcmd               int32                   `protobuf:"varint,1,opt"`
	MultimsgApplyupRsp   []*MultiMsgApplyUpRsp   `protobuf:"bytes,2,rep"`
	MultimsgApplydownRsp []*MultiMsgApplyDownRsp `protobuf:"bytes,3,rep"`
}
