// Code generated by "stringer -type=ProtocolType -linecomment"; DO NOT EDIT.

package auth

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[Unset-0]
	_ = x[AndroidPhone-1]
	_ = x[AndroidWatch-2]
	_ = x[MacOS-3]
	_ = x[QiDian-4]
	_ = x[IPad-5]
	_ = x[AndroidPad-6]
}

const _ProtocolType_name = "UnsetAndroid PhoneAndroid WatchMacOS企点iPadAndroid Pad"

var _ProtocolType_index = [...]uint8{0, 5, 18, 31, 36, 42, 46, 57}

func (i ProtocolType) String() string {
	if i < 0 || i >= ProtocolType(len(_ProtocolType_index)-1) {
		return "ProtocolType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _ProtocolType_name[_ProtocolType_index[i]:_ProtocolType_index[i+1]]
}
