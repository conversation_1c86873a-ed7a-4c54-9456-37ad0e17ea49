syntax = "proto2";

option go_package = "github.com/Mrs4s/MiraiGo/client/pb/exciting";

message FileUploadExt {
  optional int32 unknown1 = 1;
  optional int32 unknown2 = 2;
  optional int32 unknown3 = 3;
  optional FileUploadEntry entry = 100;
  optional int32 unknown200 = 200;
}

message FileUploadEntry {
  optional ExcitingBusiInfo busiBuff = 100;
  optional ExcitingFileEntry fileEntry = 200;
  optional ExcitingClientInfo clientInfo = 300;
  optional ExcitingFileNameInfo fileNameInfo = 400;
  optional ExcitingHostConfig host = 500;
}

message ExcitingBusiInfo {
  optional int32 busId = 1;
  optional int64 senderUin = 100;
  optional int64 receiverUin = 200; // probable
  optional int64 groupCode = 400; // probable
}

message ExcitingFileEntry {
  optional int64 fileSize = 100;
  optional bytes md5 = 200;
  optional bytes sha1 = 300;
  optional bytes fileId = 600;
  optional bytes uploadKey = 700;
}

message ExcitingClientInfo {
  optional int32 clientType = 100; // probable
  optional string appId = 200;
  optional int32 terminalType = 300; // probable
  optional string clientVer = 400;
  optional int32 unknown = 600;
}

message ExcitingFileNameInfo {// probable
  optional string fileName = 100;
}

message ExcitingHostConfig {
  repeated ExcitingHostInfo  hosts = 200;
}

message ExcitingHostInfo {
  optional ExcitingUrlInfo url = 1;
  optional int32 port = 2;
}

message ExcitingUrlInfo {
  optional int32 unknown = 1; // not https?
  optional string host = 2;
}