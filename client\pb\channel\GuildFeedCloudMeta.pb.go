// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/channel/GuildFeedCloudMeta.proto

package channel

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type ContentMetaData struct {
	Count     *RichTextContentCount `protobuf:"bytes,1,opt"`
	ContentID proto.Option[int64]   `protobuf:"varint,2,opt"`
	_         [0]func()
}

type FeedMetaData struct {
	Content          *ContentMetaData     `protobuf:"bytes,1,opt"`
	LastModifiedTime proto.Option[uint64] `protobuf:"varint,2,opt"`
	_                [0]func()
}

type FeedRedTouchTransInfo struct {
	FeedId         proto.Option[string] `protobuf:"bytes,1,opt"`
	Author         proto.Option[string] `protobuf:"bytes,2,opt"`
	CreateTs       proto.Option[int64]  `protobuf:"varint,3,opt"`
	MsgType        proto.Option[int32]  `protobuf:"varint,4,opt"`
	PageType       proto.Option[int32]  `protobuf:"varint,5,opt"`
	RedType        proto.Option[int32]  `protobuf:"varint,6,opt"`
	InsertPageType proto.Option[int32]  `protobuf:"varint,7,opt"`
	_              [0]func()
}

type NoticeOperation struct {
	Type   proto.Option[uint32] `protobuf:"varint,1,opt"`
	Schema proto.Option[string] `protobuf:"bytes,2,opt"`
	_      [0]func()
}

type RichTextContentCount struct {
	TextWord proto.Option[uint64] `protobuf:"varint,1,opt"`
	At       proto.Option[uint64] `protobuf:"varint,2,opt"`
	Url      proto.Option[uint64] `protobuf:"varint,3,opt"`
	Emoji    proto.Option[uint64] `protobuf:"varint,4,opt"`
	Image    proto.Option[uint64] `protobuf:"varint,5,opt"`
	Video    proto.Option[uint64] `protobuf:"varint,6,opt"`
	_        [0]func()
}

type StAnimation struct {
	Width        proto.Option[uint32] `protobuf:"varint,1,opt"`
	Height       proto.Option[uint32] `protobuf:"varint,2,opt"`
	AnimationUrl proto.Option[string] `protobuf:"bytes,3,opt"`
	BusiData     []byte               `protobuf:"bytes,4,opt"`
}

type StBusiReportInfo struct {
	RecomReport *StRecomReportInfo   `protobuf:"bytes,1,opt"`
	TraceID     proto.Option[string] `protobuf:"bytes,2,opt"`
	_           [0]func()
}

type StChannelShareInfo struct {
	FeedID           proto.Option[string] `protobuf:"bytes,1,opt"`
	PosterID         proto.Option[string] `protobuf:"bytes,2,opt"`
	FeedPublishAt    proto.Option[uint64] `protobuf:"varint,3,opt"`
	ChannelSign      *StChannelSign       `protobuf:"bytes,4,opt"`
	UpdateDurationMs proto.Option[uint64] `protobuf:"varint,5,opt"`
	Sign             *StChannelShareSign  `protobuf:"bytes,6,opt"`
	_                [0]func()
}

type StChannelShareSign struct {
	CreateAt proto.Option[uint64] `protobuf:"varint,1,opt"`
	Token    proto.Option[string] `protobuf:"bytes,2,opt"`
	_        [0]func()
}

type StCircleRankItem struct {
	RankNo     proto.Option[int32]  `protobuf:"varint,1,opt"`
	CircleName proto.Option[string] `protobuf:"bytes,2,opt"`
	FuelValue  proto.Option[int64]  `protobuf:"varint,3,opt"`
	FeedNum    proto.Option[int64]  `protobuf:"varint,4,opt"`
	CircleID   proto.Option[string] `protobuf:"bytes,5,opt"`
	_          [0]func()
}

type StClientInfo struct {
	Feedclientkey proto.Option[string] `protobuf:"bytes,1,opt"`
	ClientMap     []*CommonEntry       `protobuf:"bytes,2,rep"`
}

type StComment struct {
	Id           proto.Option[string] `protobuf:"bytes,1,opt"`
	PostUser     *StUser              `protobuf:"bytes,2,opt"`
	CreateTime   proto.Option[uint64] `protobuf:"varint,3,opt"`
	Content      proto.Option[string] `protobuf:"bytes,4,opt"`
	ReplyCount   proto.Option[uint32] `protobuf:"varint,5,opt"`
	VecReply     []*StReply           `protobuf:"bytes,6,rep"`
	BusiData     []byte               `protobuf:"bytes,7,opt"`
	LikeInfo     *StLike              `protobuf:"bytes,8,opt"`
	TypeFlag     proto.Option[uint32] `protobuf:"varint,9,opt"`
	AtUinList    []string             `protobuf:"bytes,10,rep"`
	TypeFlag2    proto.Option[uint32] `protobuf:"varint,11,opt"`
	CreateTimeNs proto.Option[uint64] `protobuf:"varint,12,opt"`
	StoreExtInfo []*CommonEntry       `protobuf:"bytes,13,rep"`
	ThirdId      proto.Option[string] `protobuf:"bytes,14,opt"`
	SourceType   proto.Option[uint32] `protobuf:"varint,15,opt"`
	RichContents *StRichText          `protobuf:"bytes,16,opt"`
}

type StDebugInfo struct {
	DebugMap []*CommonEntry `protobuf:"bytes,1,rep"`
}

type StDittoFeed struct {
	DittoId        proto.Option[uint32] `protobuf:"varint,1,opt"`
	DittoPatternId proto.Option[uint32] `protobuf:"varint,2,opt"`
	DittoData      []byte               `protobuf:"bytes,3,opt"`
	DittoDataNew   []byte               `protobuf:"bytes,4,opt"`
}

type StExifInfo struct {
	Kvs []*CommonEntry `protobuf:"bytes,1,rep"`
}

type StExternalMedalWallInfo struct {
	NeedRedPoint     proto.Option[bool]   `protobuf:"varint,1,opt"`
	MedalInfos       []*StMedalInfo       `protobuf:"bytes,2,rep"`
	MedalWallJumpUrl proto.Option[string] `protobuf:"bytes,3,opt"`
	NeedShowEntrance proto.Option[bool]   `protobuf:"varint,4,opt"`
}

type StFeed struct {
	Id              proto.Option[string]   `protobuf:"bytes,1,opt"`
	Title           *StRichText            `protobuf:"bytes,2,opt"`
	Subtitle        *StRichText            `protobuf:"bytes,3,opt"`
	Poster          *StUser                `protobuf:"bytes,4,opt"`
	Videos          []*StVideo             `protobuf:"bytes,5,rep"`
	Contents        *StRichText            `protobuf:"bytes,6,opt"`
	CreateTime      proto.Option[uint64]   `protobuf:"varint,7,opt"`
	EmotionReaction *StEmotionReactionInfo `protobuf:"bytes,8,opt"`
	CommentCount    proto.Option[uint32]   `protobuf:"varint,9,opt"`
	VecComment      []*StComment           `protobuf:"bytes,10,rep"`
	Share           *StShare               `protobuf:"bytes,11,opt"`
	VisitorInfo     *StVisitor             `protobuf:"bytes,12,opt"`
	Images          []*StImage             `protobuf:"bytes,13,rep"`
	PoiInfo         *StPoiInfoV2           `protobuf:"bytes,14,opt"`
	TagInfos        []*StTagInfo           `protobuf:"bytes,15,rep"`
	BusiReport      []byte                 `protobuf:"bytes,16,opt"`
	OpMask          []uint32               `protobuf:"varint,17,rep"`
	Opinfo          *StOpinfo              `protobuf:"bytes,18,opt"`
	ExtInfo         []*CommonEntry         `protobuf:"bytes,19,rep"`
	PatternInfo     proto.Option[string]   `protobuf:"bytes,20,opt"`
	ChannelInfo     *StChannelInfo         `protobuf:"bytes,21,opt"`
	CreateTimeNs    proto.Option[uint64]   `protobuf:"varint,22,opt"`
	Summary         *StFeedSummary         `protobuf:"bytes,23,opt"`
	RecomInfo       *StRecomInfo           `protobuf:"bytes,24,opt"`
	Meta            *FeedMetaData          `protobuf:"bytes,25,opt"`
}

type StFeedAbstract struct {
	Id         proto.Option[string] `protobuf:"bytes,1,opt"`
	Title      proto.Option[string] `protobuf:"bytes,2,opt"`
	Poster     *StUser              `protobuf:"bytes,3,opt"`
	Pic        *StImage             `protobuf:"bytes,4,opt"`
	Type       proto.Option[uint32] `protobuf:"varint,5,opt"`
	CreateTime proto.Option[uint64] `protobuf:"varint,6,opt"`
	Video      *StVideo             `protobuf:"bytes,7,opt"`
	FuelNum    proto.Option[uint32] `protobuf:"varint,8,opt"`
	Content    proto.Option[string] `protobuf:"bytes,9,opt"`
	Images     []*StImage           `protobuf:"bytes,10,rep"`
	CountInfo  *StFeedCount         `protobuf:"bytes,11,opt"`
}

type StFeedCount struct {
	Liked   proto.Option[int64] `protobuf:"varint,1,opt"`
	Push    proto.Option[int64] `protobuf:"varint,2,opt"`
	Comment proto.Option[int64] `protobuf:"varint,3,opt"`
	Visitor proto.Option[int64] `protobuf:"varint,4,opt"`
	_       [0]func()
}

type StFeedSummary struct {
	LayoutType proto.Option[uint32] `protobuf:"varint,1,opt"`
	_          [0]func()
}

type StFollowRecomInfo struct {
	FollowText     proto.Option[string] `protobuf:"bytes,1,opt"`
	FollowUsers    []*StFollowUser      `protobuf:"bytes,4,rep"`
	CommFriendText proto.Option[string] `protobuf:"bytes,6,opt"`
	CommGroupText  proto.Option[string] `protobuf:"bytes,7,opt"`
}

type StFollowUser struct {
	Uid  proto.Option[uint64] `protobuf:"varint,1,opt"`
	Nick proto.Option[string] `protobuf:"bytes,2,opt"`
	_    [0]func()
}

type StGPSV2 struct {
	Lat   proto.Option[int64] `protobuf:"varint,1,opt"`
	Lon   proto.Option[int64] `protobuf:"varint,2,opt"`
	EType proto.Option[int64] `protobuf:"varint,3,opt"`
	Alt   proto.Option[int64] `protobuf:"varint,4,opt"`
	_     [0]func()
}

type StGuidePublishBubble struct {
	Id              proto.Option[string] `protobuf:"bytes,1,opt"`
	BackgroundImage *StImage             `protobuf:"bytes,2,opt"`
	JumpUrl         proto.Option[string] `protobuf:"bytes,3,opt"`
	_               [0]func()
}

type StIconInfo struct {
	IconUrl40  proto.Option[string] `protobuf:"bytes,1,opt"`
	IconUrl100 proto.Option[string] `protobuf:"bytes,2,opt"`
	IconUrl140 proto.Option[string] `protobuf:"bytes,3,opt"`
	IconUrl640 proto.Option[string] `protobuf:"bytes,4,opt"`
	IconUrl    proto.Option[string] `protobuf:"bytes,5,opt"`
	_          [0]func()
}

type StImage struct {
	Width        proto.Option[uint32] `protobuf:"varint,1,opt"`
	Height       proto.Option[uint32] `protobuf:"varint,2,opt"`
	PicUrl       proto.Option[string] `protobuf:"bytes,3,opt"`
	VecImageUrl  []*StImageUrl        `protobuf:"bytes,4,rep"`
	PicId        proto.Option[string] `protobuf:"bytes,5,opt"`
	BusiData     []byte               `protobuf:"bytes,6,opt"`
	ImageMD5     proto.Option[string] `protobuf:"bytes,7,opt"`
	LayerPicUrl  proto.Option[string] `protobuf:"bytes,8,opt"`
	PatternId    proto.Option[string] `protobuf:"bytes,9,opt"`
	DisplayIndex proto.Option[uint32] `protobuf:"varint,10,opt"`
}

type StImageUrl struct {
	LevelType proto.Option[uint32] `protobuf:"varint,1,opt"`
	Url       proto.Option[string] `protobuf:"bytes,2,opt"`
	Width     proto.Option[uint32] `protobuf:"varint,3,opt"`
	Height    proto.Option[uint32] `protobuf:"varint,4,opt"`
	BusiData  []byte               `protobuf:"bytes,5,opt"`
}

type StLightInteractInfo struct {
	User     *StUser              `protobuf:"bytes,1,opt"`
	Relation *StRelationInfo      `protobuf:"bytes,2,opt"`
	Count    proto.Option[uint32] `protobuf:"varint,3,opt"`
	BusiData []byte               `protobuf:"bytes,4,opt"`
}

type StLike struct {
	Id            proto.Option[string] `protobuf:"bytes,1,opt"`
	Count         proto.Option[uint32] `protobuf:"varint,2,opt"`
	Status        proto.Option[uint32] `protobuf:"varint,3,opt"`
	VecUser       []*StUser            `protobuf:"bytes,4,rep"`
	BusiData      []byte               `protobuf:"bytes,5,opt"`
	PostUser      *StUser              `protobuf:"bytes,6,opt"`
	HasLikedCount proto.Option[uint32] `protobuf:"varint,7,opt"`
	OwnerStatus   proto.Option[uint32] `protobuf:"varint,8,opt"`
	JumpUrl       proto.Option[string] `protobuf:"bytes,9,opt"`
}

type StLiteBanner struct {
	Icon       *StImage             `protobuf:"bytes,1,opt"`
	Title      proto.Option[string] `protobuf:"bytes,2,opt"`
	JumpUrl    proto.Option[string] `protobuf:"bytes,3,opt"`
	ActivityID proto.Option[string] `protobuf:"bytes,4,opt"`
	JsonStyle  proto.Option[string] `protobuf:"bytes,5,opt"`
	ExtInfo    []*CommonEntry       `protobuf:"bytes,6,rep"`
}

type StMaterialDataNew struct {
	MaterialType proto.Option[string] `protobuf:"bytes,1,opt"`
	MaterialList []*StSingleMaterial  `protobuf:"bytes,2,rep"`
}

type StMedalInfo struct {
	Type          proto.Option[int32]  `protobuf:"varint,1,opt"`
	MedalName     proto.Option[string] `protobuf:"bytes,2,opt"`
	MedalID       proto.Option[string] `protobuf:"bytes,3,opt"`
	Rank          proto.Option[int32]  `protobuf:"varint,4,opt"`
	IsHighLight   proto.Option[bool]   `protobuf:"varint,5,opt"`
	IsNew         proto.Option[bool]   `protobuf:"varint,6,opt"`
	JumpUrl       proto.Option[string] `protobuf:"bytes,7,opt"`
	IconUrl       proto.Option[string] `protobuf:"bytes,8,opt"`
	BackgroundUrl proto.Option[string] `protobuf:"bytes,9,opt"`
	Describe      proto.Option[string] `protobuf:"bytes,10,opt"`
	ReportValue   proto.Option[int32]  `protobuf:"varint,11,opt"`
	_             [0]func()
}

type StNotice struct {
	PsvFeed     *StFeed             `protobuf:"bytes,1,opt"`
	OrigineFeed *StFeed             `protobuf:"bytes,2,opt"`
	PattonInfo  *StNoticePattonInfo `protobuf:"bytes,3,opt"`
	_           [0]func()
}

type StNoticePattonInfo struct {
	PattonType proto.Option[uint32] `protobuf:"varint,1,opt"`
	PlainTxt   *StPlainTxtInfo      `protobuf:"bytes,2,opt"`
	_          [0]func()
}

type StNoticeTxtInfo struct {
	Content            *StRichText `protobuf:"bytes,1,opt"`
	ContentOfReference *StRichText `protobuf:"bytes,2,opt"`
	_                  [0]func()
}

type StOpinfo struct {
	CreateTime []uint64 `protobuf:"varint,1,rep"`
}

type StPlainTxtInfo struct {
	TxtInfo   *StNoticeTxtInfo `protobuf:"bytes,1,opt"`
	Operation *NoticeOperation `protobuf:"bytes,2,opt"`
	_         [0]func()
}

type StPoiInfoV2 struct {
	PoiId        proto.Option[string] `protobuf:"bytes,1,opt"`
	Name         proto.Option[string] `protobuf:"bytes,2,opt"`
	PoiType      proto.Option[int32]  `protobuf:"varint,3,opt"`
	TypeName     proto.Option[string] `protobuf:"bytes,4,opt"`
	Address      proto.Option[string] `protobuf:"bytes,5,opt"`
	DistrictCode proto.Option[int32]  `protobuf:"varint,6,opt"`
	Gps          *StGPSV2             `protobuf:"bytes,7,opt"`
	Distance     proto.Option[int32]  `protobuf:"varint,8,opt"`
	HotValue     proto.Option[int32]  `protobuf:"varint,9,opt"`
	Phone        proto.Option[string] `protobuf:"bytes,10,opt"`
	Country      proto.Option[string] `protobuf:"bytes,11,opt"`
	Province     proto.Option[string] `protobuf:"bytes,12,opt"`
	City         proto.Option[string] `protobuf:"bytes,13,opt"`
	PoiNum       proto.Option[int32]  `protobuf:"varint,14,opt"`
	PoiOrderType proto.Option[int32]  `protobuf:"varint,15,opt"`
	DefaultName  proto.Option[string] `protobuf:"bytes,16,opt"`
	District     proto.Option[string] `protobuf:"bytes,17,opt"`
	DianPingId   proto.Option[string] `protobuf:"bytes,18,opt"`
	DistanceText proto.Option[string] `protobuf:"bytes,19,opt"`
	DisplayName  proto.Option[string] `protobuf:"bytes,20,opt"`
	_            [0]func()
}

type StPrePullCacheFeed struct {
	Id             proto.Option[string] `protobuf:"bytes,1,opt"`
	Poster         *StUser              `protobuf:"bytes,2,opt"`
	CreateTime     proto.Option[uint64] `protobuf:"varint,3,opt"`
	BusiTranparent []*BytesEntry        `protobuf:"bytes,4,rep"`
}

type StProxyInfo struct {
	CmdId       proto.Option[int32]  `protobuf:"varint,1,opt"`
	SubCmdId    proto.Option[int32]  `protobuf:"varint,2,opt"`
	AppProtocol proto.Option[string] `protobuf:"bytes,3,opt"`
	ReqBody     []byte               `protobuf:"bytes,4,opt"`
}

type StRankingItem struct {
	User        *StUser             `protobuf:"bytes,1,opt"`
	Relation    *StRelationInfo     `protobuf:"bytes,2,opt"`
	Score       proto.Option[int64] `protobuf:"varint,3,opt"`
	Grade       proto.Option[int32] `protobuf:"varint,4,opt"`
	BusiData    []byte              `protobuf:"bytes,5,opt"`
	RankNo      proto.Option[int32] `protobuf:"varint,6,opt"`
	InTopicList proto.Option[int32] `protobuf:"varint,7,opt"`
}

type StRecomForward struct {
	Id         proto.Option[string] `protobuf:"bytes,1,opt"`
	Title      proto.Option[string] `protobuf:"bytes,2,opt"`
	Subtitle   proto.Option[string] `protobuf:"bytes,3,opt"`
	Poster     *StUser              `protobuf:"bytes,4,opt"`
	CreateTime proto.Option[uint64] `protobuf:"varint,5,opt"`
	Type       proto.Option[uint32] `protobuf:"varint,6,opt"`
	BusiData   []byte               `protobuf:"bytes,7,opt"`
}

type StRecomInfo struct {
	RecomReason     proto.Option[string] `protobuf:"bytes,1,opt"`
	RecomAttachInfo []byte               `protobuf:"bytes,2,opt"`
	RecomTrace      proto.Option[string] `protobuf:"bytes,3,opt"`
	ClientSealData  []byte               `protobuf:"bytes,4,opt"`
	IconUrl         proto.Option[string] `protobuf:"bytes,5,opt"`
	RecomReasonType proto.Option[int32]  `protobuf:"varint,6,opt"`
}

type StRecomReportInfo struct {
	RecomInfos []*StSingleRecomReportInfo `protobuf:"bytes,1,rep"`
}

type StRelationInfo struct {
	Id            proto.Option[string] `protobuf:"bytes,1,opt"`
	Relation      proto.Option[uint32] `protobuf:"varint,2,opt"`
	BusiData      []byte               `protobuf:"bytes,3,opt"`
	RelationState proto.Option[uint32] `protobuf:"varint,4,opt"`
	Score         proto.Option[uint32] `protobuf:"varint,5,opt"`
	IsBlock       proto.Option[bool]   `protobuf:"varint,6,opt"`
	IsBlocked     proto.Option[bool]   `protobuf:"varint,7,opt"`
	IsFriend      proto.Option[bool]   `protobuf:"varint,8,opt"`
	IsUncare      proto.Option[bool]   `protobuf:"varint,9,opt"`
	ImBitMap      proto.Option[uint64] `protobuf:"varint,10,opt"`
}

type StReply struct {
	Id            proto.Option[string] `protobuf:"bytes,1,opt"`
	PostUser      *StUser              `protobuf:"bytes,2,opt"`
	CreateTime    proto.Option[uint64] `protobuf:"varint,3,opt"`
	Content       proto.Option[string] `protobuf:"bytes,4,opt"`
	TargetUser    *StUser              `protobuf:"bytes,5,opt"`
	BusiData      []byte               `protobuf:"bytes,6,opt"`
	LikeInfo      *StLike              `protobuf:"bytes,7,opt"`
	TypeFlag      proto.Option[uint32] `protobuf:"varint,8,opt"`
	Modifyflag    proto.Option[uint32] `protobuf:"varint,9,opt"`
	AtUinList     []string             `protobuf:"bytes,10,rep"`
	TypeFlag2     proto.Option[uint32] `protobuf:"varint,11,opt"`
	CreateTimeNs  proto.Option[uint64] `protobuf:"varint,12,opt"`
	StoreExtInfo  []*CommonEntry       `protobuf:"bytes,13,rep"`
	ThirdId       proto.Option[string] `protobuf:"bytes,14,opt"`
	TargetReplyID proto.Option[string] `protobuf:"bytes,15,opt"`
	SourceType    proto.Option[uint32] `protobuf:"varint,16,opt"`
	RichContents  *StRichText          `protobuf:"bytes,17,opt"`
}

type StReportInfo struct {
	Id         proto.Option[string] `protobuf:"bytes,1,opt"`
	BusiReport []byte               `protobuf:"bytes,2,opt"`
}

type StRichText struct {
	Contents []*StRichTextContent `protobuf:"bytes,1,rep"`
}

type StRichTextAtContent struct {
	Type        proto.Option[uint32]           `protobuf:"varint,1,opt"`
	GuildInfo   *GuildChannelBaseGuildInfo     `protobuf:"bytes,2,opt"`
	RoleGroupId *GuildChannelBaseRoleGroupInfo `protobuf:"bytes,3,opt"`
	User        *StUser                        `protobuf:"bytes,4,opt"`
	_           [0]func()
}

type GuildChannelBaseGuildInfo struct {
	GuildId  proto.Option[uint64] `protobuf:"varint,1,opt"`
	Name     proto.Option[string] `protobuf:"bytes,2,opt"`
	JoinTime proto.Option[uint64] `protobuf:"varint,3,opt"`
	_        [0]func()
}

type GuildChannelBaseRoleGroupInfo struct {
	RoleId proto.Option[uint64] `protobuf:"varint,1,opt"`
	Name   proto.Option[string] `protobuf:"bytes,2,opt"`
	Color  proto.Option[uint32] `protobuf:"varint,3,opt"`
	_      [0]func()
}

type StRichTextChannelContent struct {
	ChannelInfo *StChannelInfo `protobuf:"bytes,1,opt"`
	_           [0]func()
}

type StRichTextContent struct {
	Type           proto.Option[uint32]      `protobuf:"varint,1,opt"`
	PatternId      proto.Option[string]      `protobuf:"bytes,2,opt"`
	TextContent    *StRichTextTextContent    `protobuf:"bytes,3,opt"`
	AtContent      *StRichTextAtContent      `protobuf:"bytes,4,opt"`
	UrlContent     *StRichTextURLContent     `protobuf:"bytes,5,opt"`
	EmojiContent   *StRichTextEmojiContent   `protobuf:"bytes,6,opt"`
	ChannelContent *StRichTextChannelContent `protobuf:"bytes,7,opt"`
	_              [0]func()
}

type StRichTextEmojiContent struct {
	Id   proto.Option[string] `protobuf:"bytes,1,opt"`
	Type proto.Option[string] `protobuf:"bytes,2,opt"`
	Name proto.Option[string] `protobuf:"bytes,3,opt"`
	Url  proto.Option[string] `protobuf:"bytes,4,opt"`
	_    [0]func()
}

type StRichTextTextContent struct {
	Text proto.Option[string] `protobuf:"bytes,1,opt"`
	_    [0]func()
}

type StRichTextURLContent struct {
	Url         proto.Option[string] `protobuf:"bytes,1,opt"`
	DisplayText proto.Option[string] `protobuf:"bytes,2,opt"`
	_           [0]func()
}

type StSameTopicGuideInfo struct {
	IsSameTopicGuide proto.Option[uint32] `protobuf:"varint,1,opt"`
	StayShowTime     proto.Option[int64]  `protobuf:"varint,2,opt"`
	HashTag          proto.Option[string] `protobuf:"bytes,3,opt"`
	Words            proto.Option[string] `protobuf:"bytes,4,opt"`
	JumpUrl          proto.Option[string] `protobuf:"bytes,5,opt"`
	ReportExt        proto.Option[string] `protobuf:"bytes,6,opt"`
	_                [0]func()
}

type StShare struct {
	Title            proto.Option[string] `protobuf:"bytes,1,opt"`
	Desc             proto.Option[string] `protobuf:"bytes,2,opt"`
	Type             proto.Option[uint32] `protobuf:"varint,3,opt"`
	Url              proto.Option[string] `protobuf:"bytes,4,opt"`
	Author           *StUser              `protobuf:"bytes,5,opt"`
	Poster           *StUser              `protobuf:"bytes,6,opt"`
	Videos           []*StVideo           `protobuf:"bytes,7,rep"`
	Shorturl         proto.Option[string] `protobuf:"bytes,8,opt"`
	ShareCardInfo    proto.Option[string] `protobuf:"bytes,9,opt"`
	ShareQzoneInfo   *StShareQzoneInfo    `protobuf:"bytes,10,opt"`
	Images           []*StImage           `protobuf:"bytes,11,rep"`
	PublishTotalUser proto.Option[uint32] `protobuf:"varint,12,opt"`
	SharedCount      proto.Option[uint32] `protobuf:"varint,13,opt"`
	ChannelShareInfo *StChannelShareInfo  `protobuf:"bytes,14,opt"`
}

type StShareQzoneInfo struct {
	Entrys []*CommonEntry `protobuf:"bytes,1,rep"`
}

type StSingleMaterial struct {
	MaterialId proto.Option[string] `protobuf:"bytes,1,opt"`
	_          [0]func()
}

type StSingleRecomReportInfo struct {
	ReportID   proto.Option[string] `protobuf:"bytes,1,opt"`
	ReportData []byte               `protobuf:"bytes,2,opt"`
}

type StTagInfo struct {
	TagId           proto.Option[string] `protobuf:"bytes,1,opt"`
	TagName         proto.Option[string] `protobuf:"bytes,2,opt"`
	TagDec          proto.Option[string] `protobuf:"bytes,3,opt"`
	UserList        []*StUser            `protobuf:"bytes,4,rep"`
	FeedList        []*StFeedAbstract    `protobuf:"bytes,5,rep"`
	TagTotalUser    proto.Option[uint32] `protobuf:"varint,6,opt"`
	TagTotalFeed    proto.Option[uint32] `protobuf:"varint,7,opt"`
	TagWording      proto.Option[string] `protobuf:"bytes,8,opt"`
	TagType         proto.Option[uint32] `protobuf:"varint,9,opt"`
	FollowState     proto.Option[uint32] `protobuf:"varint,10,opt"`
	ShareInfo       *StShare             `protobuf:"bytes,11,opt"`
	IsTop           proto.Option[uint32] `protobuf:"varint,12,opt"`
	IsSelected      proto.Option[uint32] `protobuf:"varint,13,opt"`
	UserViewHistory proto.Option[int64]  `protobuf:"varint,14,opt"`
	Medal           *StTagMedalInfo      `protobuf:"bytes,15,opt"`
	Status          proto.Option[uint32] `protobuf:"varint,16,opt"`
	OptInfo         *StTagOperateInfo    `protobuf:"bytes,17,opt"`
	TagBaseStatus   proto.Option[uint32] `protobuf:"varint,18,opt"`
	IsRecommend     proto.Option[int32]  `protobuf:"varint,19,opt"`
	TagViewHistory  proto.Option[int64]  `protobuf:"varint,20,opt"`
	OperateIconUrl  proto.Option[string] `protobuf:"bytes,21,opt"`
	TagReport       proto.Option[string] `protobuf:"bytes,99,opt"`
	TagIconUrl      proto.Option[string] `protobuf:"bytes,100,opt"`
}

type StTagMedalInfo struct {
	TagID   proto.Option[string] `protobuf:"bytes,1,opt"`
	TagName proto.Option[string] `protobuf:"bytes,2,opt"`
	Rank    proto.Option[uint64] `protobuf:"varint,3,opt"`
	_       [0]func()
}

type StTagOperateInfo struct {
	CreateUser        proto.Option[string] `protobuf:"bytes,1,opt"`
	CoverURL          proto.Option[string] `protobuf:"bytes,2,opt"`
	Desc              proto.Option[string] `protobuf:"bytes,3,opt"`
	BackgroundURL     proto.Option[string] `protobuf:"bytes,4,opt"`
	BannerURL         proto.Option[string] `protobuf:"bytes,5,opt"`
	BannerSkipLink    proto.Option[string] `protobuf:"bytes,6,opt"`
	ActivityStartTime proto.Option[int64]  `protobuf:"varint,7,opt"`
	ActivityEndTime   proto.Option[int64]  `protobuf:"varint,8,opt"`
	RecommendReason   proto.Option[string] `protobuf:"bytes,9,opt"`
	IsWhite           proto.Option[int32]  `protobuf:"varint,10,opt"`
	BeWhiteStartTime  proto.Option[int64]  `protobuf:"varint,11,opt"`
	BeWhiteEndTime    proto.Option[int64]  `protobuf:"varint,12,opt"`
	PublishSchema     proto.Option[string] `protobuf:"bytes,13,opt"`
	_                 [0]func()
}

type StUnifiedTag struct {
	UnifiedType proto.Option[string] `protobuf:"bytes,1,opt"`
	UnifiedId   proto.Option[string] `protobuf:"bytes,2,opt"`
	_           [0]func()
}

type StUser struct {
	Id                proto.Option[string]             `protobuf:"bytes,1,opt"`
	Nick              proto.Option[string]             `protobuf:"bytes,2,opt"`
	Icon              *StIconInfo                      `protobuf:"bytes,3,opt"`
	Desc              proto.Option[string]             `protobuf:"bytes,4,opt"`
	FollowState       proto.Option[uint32]             `protobuf:"varint,5,opt"`
	Type              proto.Option[uint32]             `protobuf:"varint,6,opt"`
	Sex               proto.Option[uint32]             `protobuf:"varint,7,opt"`
	Birthday          proto.Option[uint64]             `protobuf:"varint,8,opt"`
	School            proto.Option[string]             `protobuf:"bytes,9,opt"`
	Location          proto.Option[string]             `protobuf:"bytes,11,opt"`
	BusiData          []byte                           `protobuf:"bytes,12,opt"`
	FrdState          proto.Option[uint32]             `protobuf:"varint,13,opt"`
	RelationState     proto.Option[uint32]             `protobuf:"varint,14,opt"`
	BlackState        proto.Option[uint32]             `protobuf:"varint,15,opt"`
	Medal             *StTagMedalInfo                  `protobuf:"bytes,16,opt"`
	Constellation     proto.Option[int32]              `protobuf:"varint,17,opt"`
	JumpUrl           proto.Option[string]             `protobuf:"bytes,18,opt"`
	LocationCode      proto.Option[string]             `protobuf:"bytes,19,opt"`
	ThirdId           proto.Option[string]             `protobuf:"bytes,20,opt"`
	Company           proto.Option[string]             `protobuf:"bytes,21,opt"`
	CertificationDesc proto.Option[string]             `protobuf:"bytes,22,opt"`
	DescType          proto.Option[uint32]             `protobuf:"varint,23,opt"`
	ChannelUserInfo   *GuildChannelBaseChannelUserInfo `protobuf:"bytes,24,opt"`
	LoginId           proto.Option[string]             `protobuf:"bytes,25,opt"`
}

type GuildChannelBaseChannelUserInfo struct {
	ClientIdentity *ClientIdentity      `protobuf:"bytes,1,opt"`
	MemberType     proto.Option[uint32] `protobuf:"varint,2,opt"`
	// optional ChannelUserPermission permission = 3;
	RoleGroups []*GuildChannelBaseRoleGroupInfo `protobuf:"bytes,4,rep"`
}

type StUserGroupInfo struct {
	Id       proto.Option[string] `protobuf:"bytes,1,opt"`
	Name     proto.Option[string] `protobuf:"bytes,2,opt"`
	UserList []*StUser            `protobuf:"bytes,3,rep"`
}

type StUserRecomInfo struct {
	User     *StUser           `protobuf:"bytes,1,opt"`
	FeedList []*StFeedAbstract `protobuf:"bytes,2,rep"`
	BusiData []byte            `protobuf:"bytes,3,opt"`
}

type StVideo struct {
	FileId            proto.Option[string]  `protobuf:"bytes,1,opt"`
	FileSize          proto.Option[uint32]  `protobuf:"varint,2,opt"`
	Duration          proto.Option[uint32]  `protobuf:"varint,3,opt"`
	Width             proto.Option[uint32]  `protobuf:"varint,4,opt"`
	Height            proto.Option[uint32]  `protobuf:"varint,5,opt"`
	PlayUrl           proto.Option[string]  `protobuf:"bytes,6,opt"`
	TransStatus       proto.Option[uint32]  `protobuf:"varint,7,opt"`
	VideoPrior        proto.Option[uint32]  `protobuf:"varint,8,opt"`
	VideoRate         proto.Option[uint32]  `protobuf:"varint,9,opt"`
	VecVideoUrl       []*StVideoUrl         `protobuf:"bytes,10,rep"`
	BusiData          []byte                `protobuf:"bytes,11,opt"`
	ApprovalStatus    proto.Option[uint32]  `protobuf:"varint,12,opt"`
	VideoSource       proto.Option[uint32]  `protobuf:"varint,13,opt"`
	MediaQualityRank  proto.Option[uint32]  `protobuf:"varint,14,opt"`
	MediaQualityScore proto.Option[float32] `protobuf:"fixed32,15,opt"`
	VideoMD5          proto.Option[string]  `protobuf:"bytes,16,opt"`
	IsQuic            proto.Option[uint32]  `protobuf:"varint,17,opt"`
	Orientation       proto.Option[uint32]  `protobuf:"varint,18,opt"`
	Cover             *StImage              `protobuf:"bytes,19,opt"`
	PatternId         proto.Option[string]  `protobuf:"bytes,20,opt"`
	DisplayIndex      proto.Option[uint32]  `protobuf:"varint,21,opt"`
}

type StVideoUrl struct {
	LevelType    proto.Option[uint32] `protobuf:"varint,1,opt"`
	PlayUrl      proto.Option[string] `protobuf:"bytes,2,opt"`
	VideoPrior   proto.Option[uint32] `protobuf:"varint,3,opt"`
	VideoRate    proto.Option[uint32] `protobuf:"varint,4,opt"`
	TransStatus  proto.Option[uint32] `protobuf:"varint,5,opt"`
	BusiData     []byte               `protobuf:"bytes,6,opt"`
	HasWatermark proto.Option[bool]   `protobuf:"varint,7,opt"`
}

type StVisitor struct {
	ViewCount  proto.Option[uint32] `protobuf:"varint,1,opt"`
	BusiData   []byte               `protobuf:"bytes,2,opt"`
	RecomCount proto.Option[uint32] `protobuf:"varint,3,opt"`
	ViewDesc   proto.Option[string] `protobuf:"bytes,4,opt"`
}

type StWearingMedal struct {
	MedalInfos []*StWearingMedalInfo `protobuf:"bytes,1,rep"`
}

type StWearingMedalInfo struct {
	Type      proto.Option[int32]  `protobuf:"varint,1,opt"`
	MedalName proto.Option[string] `protobuf:"bytes,2,opt"`
	MedalID   proto.Option[string] `protobuf:"bytes,3,opt"`
	_         [0]func()
}
