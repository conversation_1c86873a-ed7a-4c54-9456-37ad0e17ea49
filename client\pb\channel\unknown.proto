// 存放所有未知的结构体, 均为手动分析复原
syntax = "proto2";

package channel;

option go_package = "github.com/Mrs4s/MiraiGo/client/pb/channel";

// see sub_37628C
message ChannelOidb0xf5bRsp {
  optional uint64 guildId = 1;
  repeated GuildMemberInfo bots = 4;
  repeated GuildMemberInfo members = 5;
  optional uint32 nextIndex = 10;
  optional uint32 finished = 9;
  optional string nextQueryParam = 24;
  repeated GuildGroupMembersInfo memberWithRoles = 25;
  optional uint64 nextRoleIdIndex = 26;
}

message ChannelOidb0xf88Rsp {
  optional GuildUserProfile profile = 1;
}

message ChannelOidb0xfc9Rsp {
  optional GuildUserProfile profile = 1;
}

message ChannelOidb0xf57Rsp {
  optional GuildMetaRsp rsp = 1;
}

message ChannelOidb0xf55Rsp {
  optional GuildChannelInfo info = 1;
}

message ChannelOidb0xf5dRsp {
  optional ChannelListRsp rsp = 1;
}

message ChannelOidb0x1017Rsp {
  optional P10x1017 p1 = 1;
}

message P10x1017 {
  optional uint64 tinyId = 1;
  repeated GuildUserRole roles = 3;
}

message ChannelOidb0x1019Rsp {
  optional uint64 guildId = 1;
  repeated GuildRole roles = 2;
  // 3: ?
  // 4: 
}

/*
message ChannelOidb0x100dReq { // 修改身份组
  optional uint64 guildId = 1;
  repeated uint64 roleId = 2; 
  repeated int32 unkonwn = 3; // 3: ? 三个1
  repeated ModifyGuildRole role = 4;
}*/

/*
message ChannelOidb0x1016Req { // 新建身份组
  optional uint64 guildId = 1;
  repeated int32 unknown = 2; // 2: ? 三个1
  optional ModifyGuildRole role = 3;
  repeated uint64 initialUsers = 4;
}*/

message ChannelOidb0x1016Rsp {
  optional uint64 roleId = 2;
}

/*
message ChannelOidb0x101aReq { // 修改身份组
  optional uint64 guildId = 1;
  repeated SetGuildRole setRoles = 2;
  repeated SetGuildRole removeRoles = 3;
}*/

message GuildMetaRsp {
  optional uint64 guildId = 3;
  optional GuildMeta meta = 4;
}

message ChannelListRsp {
  optional uint64 guildId = 1;
  repeated GuildChannelInfo channels = 2;
  // 5: Category infos
}

message GuildGroupMembersInfo {
  optional uint64 roleId = 1;
  repeated GuildMemberInfo members = 2;
  optional string roleName = 3;
  optional uint32 color = 4;
}

// see sub_374334
message GuildMemberInfo {
  optional string title = 2;
  optional string nickname = 3;
  optional int64 lastSpeakTime = 4; // uncertainty
  optional int32 role = 5; // uncertainty
  optional uint64 tinyId = 8;
}

// 频道系统用户资料
message GuildUserProfile {
  optional uint64 tinyId = 2;
  optional string nickname = 3;
  optional string avatarUrl = 6;
  // 15: avatar url info
  optional int64 joinTime = 16;  // uncertainty
  // 22 cards
  // 23 display cards
  // 25 current cards *uncertainty
}

message GuildRole {
  optional uint64 roleId = 1;
  optional string name = 2;
  optional uint32 argbColor = 3;
  optional int32 independent = 4;
  optional int32 num = 5;
  optional int32 owned = 6; // 是否拥有 存疑
  optional int32 disabled = 7; // 权限不足或不显示
  optional int32 maxNum = 8;
  // 9: ?
}

message GuildUserRole {
  optional uint64 roleId = 1;
  optional string name = 2;
  optional uint32 argbColor = 3;
  optional int32 independent = 4;
}

/*
message SetGuildRole {
  optional uint64 roleId = 1;
  optional uint64 targetId = 2;
}*/

/*
message ModifyGuildRole {
  optional string roleName = 1;
  optional uint32 color = 2;
  optional int32 independent = 3; // 身份组单独显示
}*/

message GuildMeta {
  optional uint64 guildCode = 2;
  optional int64 createTime = 4;
  optional int64 maxMemberCount = 5;
  optional int64 memberCount = 6;
  optional string name = 8;
  optional int32 robotMaxNum = 11;
  optional int32 adminMaxNum = 12;
  optional string profile = 13;
  optional int64 avatarSeq = 14;
  optional uint64 ownerId = 18;
  optional int64 coverSeq = 19;
  optional int32 clientId = 20;
}

message GuildChannelInfo {
  optional uint64 channelId = 1;
  optional string channelName = 2;
  optional int64 creatorUin = 3;
  optional int64 createTime = 4;
  optional uint64 guildId = 5;
  optional int32 finalNotifyType = 6;
  optional int32 channelType = 7;
  optional int32 talkPermission = 8;
  // 11 - 14 : MsgInfo
  optional uint64 creatorTinyId = 15;
  // 16: Member info ?
  optional int32 visibleType = 22;
  optional GuildChannelTopMsgInfo topMsg = 28;
  optional int32 currentSlowModeKey = 31;
  repeated GuildChannelSlowModeInfo slowModeInfos = 32;
}

message GuildChannelSlowModeInfo {
  optional int32 slowModeKey = 1;
  optional int32 speakFrequency = 2;
  optional int32 slowModeCircle = 3;
  optional string slowModeText = 4;
}

message GuildChannelTopMsgInfo {
  optional uint64 topMsgSeq = 1;
  optional int64 topMsgTime = 2;
  optional uint64 topMsgOperatorTinyId = 3;
}
/*
// 个性档案卡片
message GuildMemberProfileCard {
  optional int32 appid = 1;
  optional string name = 2;

}
 */

