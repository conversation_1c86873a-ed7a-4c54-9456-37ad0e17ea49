// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/channel/msgpush.proto

package channel

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type FocusInfo struct {
	ChannelIdList []uint64 `protobuf:"varint,1,rep"`
}

type MsgOnlinePush struct {
	Msgs         []*ChannelMsgContent `protobuf:"bytes,1,rep"`
	GeneralFlag  proto.Option[uint32] `protobuf:"varint,2,opt"`
	NeedResp     proto.Option[uint32] `protobuf:"varint,3,opt"`
	ServerBuf    []byte               `protobuf:"bytes,4,opt"`
	CompressFlag proto.Option[uint32] `protobuf:"varint,5,opt"`
	CompressMsg  []byte               `protobuf:"bytes,6,opt"`
	FocusInfo    *FocusInfo           `protobuf:"bytes,7,opt"`
	HugeFlag     proto.Option[uint32] `protobuf:"varint,8,opt"`
}

type MsgPushResp struct {
	ServerBuf []byte `protobuf:"bytes,1,opt"`
}

type PressMsg struct {
	Msgs []*ChannelMsgContent `protobuf:"bytes,1,rep"`
}

type ServerBuf struct {
	SvrIp   proto.Option[uint32] `protobuf:"varint,1,opt"`
	SvrPort proto.Option[uint32] `protobuf:"varint,2,opt"`
	EchoKey []byte               `protobuf:"bytes,3,opt"`
}
