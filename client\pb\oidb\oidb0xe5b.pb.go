// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0xe5b.proto

package oidb

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type LifeAchievementItem struct {
	AchievementId      proto.Option[uint32] `protobuf:"varint,1,opt"`
	AchievementTitle   proto.Option[string] `protobuf:"bytes,2,opt"`
	AchievementIcon    proto.Option[string] `protobuf:"bytes,3,opt"`
	HasPraised         proto.Option[bool]   `protobuf:"varint,4,opt"`
	PraiseNum          proto.Option[uint32] `protobuf:"varint,5,opt"`
	AchievementContent []byte               `protobuf:"bytes,6,opt"`
}

type DE5BReqBody struct {
	Uin                   proto.Option[uint64] `protobuf:"varint,1,opt"`
	AchievementId         []uint32             `protobuf:"varint,2,rep"`
	MaxCount              proto.Option[uint32] `protobuf:"varint,3,opt"`
	ReqAchievementContent proto.Option[bool]   `protobuf:"varint,4,opt"`
}

type DE5BRspBody struct {
	AchievementTotalCount proto.Option[uint32]   `protobuf:"varint,1,opt"`
	LifeAchItem           []*LifeAchievementItem `protobuf:"bytes,2,rep"`
	AchievementOpenid     proto.Option[string]   `protobuf:"bytes,3,opt"`
}
