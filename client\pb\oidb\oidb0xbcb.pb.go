// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0xbcb.proto

package oidb

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type CheckUrlReq struct {
	Url         []string             `protobuf:"bytes,1,rep"`
	Refer       proto.Option[string] `protobuf:"bytes,2,opt"`
	Plateform   proto.Option[string] `protobuf:"bytes,3,opt"`
	QqPfTo      proto.Option[string] `protobuf:"bytes,4,opt"`
	Type        proto.Option[uint32] `protobuf:"varint,5,opt"`
	From        proto.Option[uint32] `protobuf:"varint,6,opt"`
	Chatid      proto.Option[uint64] `protobuf:"varint,7,opt"`
	ServiceType proto.Option[uint64] `protobuf:"varint,8,opt"`
	SendUin     proto.Option[uint64] `protobuf:"varint,9,opt"`
	ReqType     proto.Option[string] `protobuf:"bytes,10,opt"`
	OriginalUrl proto.Option[string] `protobuf:"bytes,11,opt"`
	IsArk       proto.Option[bool]   `protobuf:"varint,12,opt"`
	ArkName     proto.Option[string] `protobuf:"bytes,13,opt"`
	IsFinish    proto.Option[bool]   `protobuf:"varint,14,opt"`
	SrcUrls     []string             `protobuf:"bytes,15,rep"`
	SrcPlatform proto.Option[uint32] `protobuf:"varint,16,opt"`
	Qua         proto.Option[string] `protobuf:"bytes,17,opt"`
}

type CheckUrlReqItem struct {
	Url         proto.Option[string] `protobuf:"bytes,1,opt"`
	Refer       proto.Option[string] `protobuf:"bytes,2,opt"`
	Plateform   proto.Option[string] `protobuf:"bytes,3,opt"`
	QqPfTo      proto.Option[string] `protobuf:"bytes,4,opt"`
	Type        proto.Option[uint32] `protobuf:"varint,5,opt"`
	From        proto.Option[uint32] `protobuf:"varint,6,opt"`
	Chatid      proto.Option[uint64] `protobuf:"varint,7,opt"`
	ServiceType proto.Option[uint64] `protobuf:"varint,8,opt"`
	SendUin     proto.Option[uint64] `protobuf:"varint,9,opt"`
	ReqType     proto.Option[string] `protobuf:"bytes,10,opt"`
	_           [0]func()
}

type CheckUrlRsp struct {
	Results         []*UrlCheckResult    `protobuf:"bytes,1,rep"`
	NextReqDuration proto.Option[uint32] `protobuf:"varint,2,opt"`
}

type DBCBReqBody struct {
	NotUseCache proto.Option[int32] `protobuf:"varint,9,opt"`
	CheckUrlReq *CheckUrlReq        `protobuf:"bytes,10,opt"`
	_           [0]func()
}

type DBCBRspBody struct {
	Wording     proto.Option[string] `protobuf:"bytes,1,opt"`
	CheckUrlRsp *CheckUrlRsp         `protobuf:"bytes,10,opt"`
	_           [0]func()
}

type UrlCheckResult struct {
	Url          proto.Option[string] `protobuf:"bytes,1,opt"`
	Result       proto.Option[uint32] `protobuf:"varint,2,opt"`
	JumpResult   proto.Option[uint32] `protobuf:"varint,3,opt"`
	JumpUrl      proto.Option[string] `protobuf:"bytes,4,opt"`
	Level        proto.Option[uint32] `protobuf:"varint,5,opt"`
	SubLevel     proto.Option[uint32] `protobuf:"varint,6,opt"`
	Umrtype      proto.Option[uint32] `protobuf:"varint,7,opt"`
	RetFrom      proto.Option[uint32] `protobuf:"varint,8,opt"`
	OperationBit proto.Option[uint64] `protobuf:"varint,9,opt"`
	_            [0]func()
}
