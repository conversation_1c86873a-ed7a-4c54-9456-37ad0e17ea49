// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/faceroam/faceroam.proto

package faceroam

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type PlatInfo struct {
	Implat proto.Option[int64]  `protobuf:"varint,1,opt"`
	Osver  proto.Option[string] `protobuf:"bytes,2,opt"`
	Mqqver proto.Option[string] `protobuf:"bytes,3,opt"`
	_      [0]func()
}

type FaceroamReqBody struct {
	Comm          *PlatInfo            `protobuf:"bytes,1,opt"`
	Uin           proto.Option[uint64] `protobuf:"varint,2,opt"`
	SubCmd        proto.Option[uint32] `protobuf:"varint,3,opt"`
	ReqUserInfo   *ReqUserInfo         `protobuf:"bytes,4,opt"`
	ReqDeleteItem *ReqDeleteItem       `protobuf:"bytes,5,opt"`
	_             [0]func()
}

type ReqDeleteItem struct {
	Filename []string `protobuf:"bytes,1,rep"`
}

type ReqUserInfo struct {
	_ [0]func()
}

type FaceroamRspBody struct {
	Ret           proto.Option[int64]  `protobuf:"varint,1,opt"`
	Errmsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	SubCmd        proto.Option[uint32] `protobuf:"varint,3,opt"`
	RspUserInfo   *RspUserInfo         `protobuf:"bytes,4,opt"`
	RspDeleteItem *RspDeleteItem       `protobuf:"bytes,5,opt"`
	_             [0]func()
}

type RspDeleteItem struct {
	Filename []string `protobuf:"bytes,1,rep"`
	Ret      []int64  `protobuf:"varint,2,rep"`
}

type RspUserInfo struct {
	Filename    []string             `protobuf:"bytes,1,rep"`
	DeleteFile  []string             `protobuf:"bytes,2,rep"`
	Bid         proto.Option[string] `protobuf:"bytes,3,opt"`
	MaxRoamSize proto.Option[uint32] `protobuf:"varint,4,opt"`
	EmojiType   []uint32             `protobuf:"varint,5,rep"`
}
