// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0x5eb.proto

package oidb

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type D5EBReqBody struct {
	Uins                              []uint64             `protobuf:"varint,1,rep"`
	MaxPackageSize                    proto.Option[uint32] `protobuf:"varint,3,opt"`
	Openid                            [][]byte             `protobuf:"bytes,4,rep"`
	Appid                             proto.Option[uint32] `protobuf:"varint,5,opt"`
	ReqNick                           proto.Option[uint32] `protobuf:"varint,20002,opt"`
	ReqCountry                        proto.Option[uint32] `protobuf:"varint,20003,opt"`
	ReqProvince                       proto.Option[int32]  `protobuf:"varint,20004,opt"`
	ReqGender                         proto.Option[int32]  `protobuf:"varint,20009,opt"`
	ReqAllow                          proto.Option[int32]  `protobuf:"varint,20014,opt"`
	ReqFaceId                         proto.Option[int32]  `protobuf:"varint,20015,opt"`
	ReqCity                           proto.Option[int32]  `protobuf:"varint,20020,opt"`
	ReqConstellation                  proto.Option[int32]  `protobuf:"varint,20022,opt"`
	ReqCommonPlace1                   proto.Option[int32]  `protobuf:"varint,20027,opt"`
	ReqMss3Bitmapextra                proto.Option[int32]  `protobuf:"varint,20030,opt"`
	ReqBirthday                       proto.Option[int32]  `protobuf:"varint,20031,opt"`
	ReqCityId                         proto.Option[int32]  `protobuf:"varint,20032,opt"`
	ReqLang1                          proto.Option[int32]  `protobuf:"varint,20033,opt"`
	ReqLang2                          proto.Option[int32]  `protobuf:"varint,20034,opt"`
	ReqLang3                          proto.Option[int32]  `protobuf:"varint,20035,opt"`
	ReqAge                            proto.Option[int32]  `protobuf:"varint,20037,opt"`
	ReqCityZoneId                     proto.Option[int32]  `protobuf:"varint,20041,opt"`
	ReqOin                            proto.Option[int32]  `protobuf:"varint,20056,opt"`
	ReqBubbleId                       proto.Option[int32]  `protobuf:"varint,20059,opt"`
	ReqMss2Identity                   proto.Option[int32]  `protobuf:"varint,21001,opt"`
	ReqMss1Service                    proto.Option[int32]  `protobuf:"varint,21002,opt"`
	ReqLflag                          proto.Option[int32]  `protobuf:"varint,21003,opt"`
	ReqExtFlag                        proto.Option[int32]  `protobuf:"varint,21004,opt"`
	ReqBasicSvrFlag                   proto.Option[int32]  `protobuf:"varint,21006,opt"`
	ReqBasicCliFlag                   proto.Option[int32]  `protobuf:"varint,21007,opt"`
	ReqFullBirthday                   proto.Option[int32]  `protobuf:"varint,26004,opt"`
	ReqFullAge                        proto.Option[int32]  `protobuf:"varint,26005,opt"`
	ReqSimpleUpdateTime               proto.Option[int32]  `protobuf:"varint,26010,opt"`
	ReqMssUpdateTime                  proto.Option[int32]  `protobuf:"varint,26011,opt"`
	ReqPstnMultiCallTime              proto.Option[int32]  `protobuf:"varint,26012,opt"`
	ReqPstnMultiLastGuideRechargeTime proto.Option[int32]  `protobuf:"varint,26013,opt"`
	ReqPstnC2CCallTime                proto.Option[int32]  `protobuf:"varint,26014,opt"`
	ReqPstnC2CLastGuideRechargeTime   proto.Option[int32]  `protobuf:"varint,26015,opt"`
	ReqGroupMemCreditFlag             proto.Option[int32]  `protobuf:"varint,27022,opt"`
	ReqFaceAddonId                    proto.Option[int32]  `protobuf:"varint,27025,opt"`
	ReqMusicGene                      proto.Option[int32]  `protobuf:"varint,27026,opt"`
	ReqStrangerNick                   proto.Option[int32]  `protobuf:"varint,27034,opt"`
	ReqStrangerDeclare                proto.Option[int32]  `protobuf:"varint,27035,opt"`
	ReqLoveStatus                     proto.Option[int32]  `protobuf:"varint,27036,opt"`
	ReqProfession                     proto.Option[int32]  `protobuf:"varint,27037,opt"`
	ReqVasColorringFlag               proto.Option[int32]  `protobuf:"varint,27041,opt"`
	ReqCharm                          proto.Option[int32]  `protobuf:"varint,27052,opt"`
	ReqApolloTimestamp                proto.Option[int32]  `protobuf:"varint,27059,opt"`
	ReqVasFontIdFlag                  proto.Option[int32]  `protobuf:"varint,27201,opt"`
	ReqGlobalGroupLevel               proto.Option[int32]  `protobuf:"varint,27208,opt"`
	ReqInvite2GroupAutoAgreeFlag      proto.Option[int32]  `protobuf:"varint,40346,opt"`
	ReqSubaccountDisplayThirdQqFlag   proto.Option[int32]  `protobuf:"varint,40348,opt"`
	NotifyPartakeLikeRankingListFlag  proto.Option[int32]  `protobuf:"varint,40350,opt"`
	ReqLightalkSwitch                 proto.Option[int32]  `protobuf:"varint,40506,opt"`
	ReqMusicRingVisible               proto.Option[int32]  `protobuf:"varint,40507,opt"`
	ReqMusicRingAutoplay              proto.Option[int32]  `protobuf:"varint,40508,opt"`
	ReqMusicRingRedpoint              proto.Option[int32]  `protobuf:"varint,40509,opt"`
	TorchDisableFlag                  proto.Option[int32]  `protobuf:"varint,40525,opt"`
	ReqVasMagicfontFlag               proto.Option[int32]  `protobuf:"varint,40530,opt"`
	ReqVipFlag                        proto.Option[int32]  `protobuf:"varint,41756,opt"`
	ReqAuthFlag                       proto.Option[int32]  `protobuf:"varint,41783,opt"`
	ReqForbidFlag                     proto.Option[int32]  `protobuf:"varint,41784,opt"`
	ReqGodForbid                      proto.Option[int32]  `protobuf:"varint,41804,opt"`
	ReqGodFlag                        proto.Option[int32]  `protobuf:"varint,41805,opt"`
	ReqCharmLevel                     proto.Option[int32]  `protobuf:"varint,41950,opt"`
	ReqCharmShown                     proto.Option[int32]  `protobuf:"varint,41973,opt"`
	ReqFreshnewsNotifyFlag            proto.Option[int32]  `protobuf:"varint,41993,opt"`
	ReqApolloVipLevel                 proto.Option[int32]  `protobuf:"varint,41999,opt"`
	ReqApolloVipFlag                  proto.Option[int32]  `protobuf:"varint,42003,opt"`
	ReqPstnC2CVip                     proto.Option[int32]  `protobuf:"varint,42005,opt"`
	ReqPstnMultiVip                   proto.Option[int32]  `protobuf:"varint,42006,opt"`
	ReqPstnEverC2CVip                 proto.Option[int32]  `protobuf:"varint,42007,opt"`
	ReqPstnEverMultiVip               proto.Option[int32]  `protobuf:"varint,42008,opt"`
	ReqPstnMultiTryFlag               proto.Option[int32]  `protobuf:"varint,42011,opt"`
	ReqPstnC2CTryFlag                 proto.Option[int32]  `protobuf:"varint,42012,opt"`
	ReqSubscribeNearbyassistantSwitch proto.Option[int32]  `protobuf:"varint,42024,opt"`
	ReqTorchbearerFlag                proto.Option[int32]  `protobuf:"varint,42051,opt"`
	PreloadDisableFlag                proto.Option[int32]  `protobuf:"varint,42073,opt"`
	ReqMedalwallFlag                  proto.Option[int32]  `protobuf:"varint,42075,opt"`
	NotifyOnLikeRankingListFlag       proto.Option[int32]  `protobuf:"varint,42092,opt"`
	ReqApolloStatus                   proto.Option[int32]  `protobuf:"varint,42980,opt"`
}

type D5EBRspBody struct {
	UinData        []*UdcUinData `protobuf:"bytes,11,rep"`
	UnfinishedUins []int64       `protobuf:"varint,12,rep"`
}

type UdcUinData struct {
	Uin                              proto.Option[int64] `protobuf:"varint,1,opt"`
	Openid                           []byte              `protobuf:"bytes,4,opt"`
	Nick                             []byte              `protobuf:"bytes,20002,opt"`
	Country                          []byte              `protobuf:"bytes,20003,opt"`
	Province                         []byte              `protobuf:"bytes,20004,opt"`
	Gender                           proto.Option[int32] `protobuf:"varint,20009,opt"`
	Allow                            proto.Option[int32] `protobuf:"varint,20014,opt"`
	FaceId                           proto.Option[int32] `protobuf:"varint,20015,opt"`
	City                             []byte              `protobuf:"bytes,20020,opt"`
	Constellation                    proto.Option[int32] `protobuf:"varint,20022,opt"`
	CommonPlace1                     proto.Option[int32] `protobuf:"varint,20027,opt"`
	Mss3Bitmapextra                  []byte              `protobuf:"bytes,20030,opt"`
	Birthday                         []byte              `protobuf:"bytes,20031,opt"`
	CityId                           []byte              `protobuf:"bytes,20032,opt"`
	Lang1                            proto.Option[int32] `protobuf:"varint,20033,opt"`
	Lang2                            proto.Option[int32] `protobuf:"varint,20034,opt"`
	Lang3                            proto.Option[int32] `protobuf:"varint,20035,opt"`
	Age                              proto.Option[int32] `protobuf:"varint,20037,opt"`
	CityZoneId                       proto.Option[int32] `protobuf:"varint,20041,opt"`
	Oin                              proto.Option[int32] `protobuf:"varint,20056,opt"`
	BubbleId                         proto.Option[int32] `protobuf:"varint,20059,opt"`
	Mss2Identity                     []byte              `protobuf:"bytes,21001,opt"`
	Mss1Service                      []byte              `protobuf:"bytes,21002,opt"`
	Lflag                            proto.Option[int32] `protobuf:"varint,21003,opt"`
	ExtFlag                          proto.Option[int32] `protobuf:"varint,21004,opt"`
	BasicSvrFlag                     []byte              `protobuf:"bytes,21006,opt"`
	BasicCliFlag                     []byte              `protobuf:"bytes,21007,opt"`
	FullBirthday                     []byte              `protobuf:"bytes,26004,opt"`
	FullAge                          []byte              `protobuf:"bytes,26005,opt"`
	SimpleUpdateTime                 proto.Option[int32] `protobuf:"varint,26010,opt"`
	MssUpdateTime                    proto.Option[int32] `protobuf:"varint,26011,opt"`
	PstnMultiCallTime                proto.Option[int32] `protobuf:"varint,26012,opt"`
	PstnMultiLastGuideRechargeTime   proto.Option[int32] `protobuf:"varint,26013,opt"`
	PstnC2CCallTime                  proto.Option[int32] `protobuf:"varint,26014,opt"`
	PstnC2CLastGuideRechargeTime     proto.Option[int32] `protobuf:"varint,26015,opt"`
	GroupMemCreditFlag               proto.Option[int32] `protobuf:"varint,27022,opt"`
	FaceAddonId                      proto.Option[int64] `protobuf:"varint,27025,opt"`
	MusicGene                        []byte              `protobuf:"bytes,27026,opt"`
	StrangerNick                     []byte              `protobuf:"bytes,27034,opt"`
	StrangerDeclare                  []byte              `protobuf:"bytes,27035,opt"`
	LoveStatus                       proto.Option[int32] `protobuf:"varint,27036,opt"`
	Profession                       proto.Option[int32] `protobuf:"varint,27037,opt"`
	VasColorringId                   proto.Option[int32] `protobuf:"varint,27041,opt"`
	Charm                            proto.Option[int32] `protobuf:"varint,27052,opt"`
	ApolloTimestamp                  proto.Option[int32] `protobuf:"varint,27059,opt"`
	VasFontId                        proto.Option[int32] `protobuf:"varint,27201,opt"`
	GlobalGroupLevel                 proto.Option[int32] `protobuf:"varint,27208,opt"`
	ReqInvite2GroupAutoAgreeFlag     proto.Option[int32] `protobuf:"varint,40346,opt"`
	SubaccountDisplayThirdQqFlag     proto.Option[int32] `protobuf:"varint,40348,opt"`
	NotifyPartakeLikeRankingListFlag proto.Option[int32] `protobuf:"varint,40350,opt"`
	LightalkSwitch                   proto.Option[int32] `protobuf:"varint,40506,opt"`
	MusicRingVisible                 proto.Option[int32] `protobuf:"varint,40507,opt"`
	MusicRingAutoplay                proto.Option[int32] `protobuf:"varint,40508,opt"`
	MusicRingRedpoint                proto.Option[int32] `protobuf:"varint,40509,opt"`
	TorchDisableFlag                 proto.Option[int32] `protobuf:"varint,40525,opt"`
	VasMagicfontFlag                 proto.Option[int32] `protobuf:"varint,40530,opt"`
	VipFlag                          proto.Option[int32] `protobuf:"varint,41756,opt"`
	AuthFlag                         proto.Option[int32] `protobuf:"varint,41783,opt"`
	ForbidFlag                       proto.Option[int32] `protobuf:"varint,41784,opt"`
	GodForbid                        proto.Option[int32] `protobuf:"varint,41804,opt"`
	GodFlag                          proto.Option[int32] `protobuf:"varint,41805,opt"`
	CharmLevel                       proto.Option[int32] `protobuf:"varint,41950,opt"`
	CharmShown                       proto.Option[int32] `protobuf:"varint,41973,opt"`
	FreshnewsNotifyFlag              proto.Option[int32] `protobuf:"varint,41993,opt"`
	ApolloVipLevel                   proto.Option[int32] `protobuf:"varint,41999,opt"`
	ApolloVipFlag                    proto.Option[int32] `protobuf:"varint,42003,opt"`
	PstnC2CVip                       proto.Option[int32] `protobuf:"varint,42005,opt"`
	PstnMultiVip                     proto.Option[int32] `protobuf:"varint,42006,opt"`
	PstnEverC2CVip                   proto.Option[int32] `protobuf:"varint,42007,opt"`
	PstnEverMultiVip                 proto.Option[int32] `protobuf:"varint,42008,opt"`
	PstnMultiTryFlag                 proto.Option[int32] `protobuf:"varint,42011,opt"`
	PstnC2CTryFlag                   proto.Option[int32] `protobuf:"varint,42012,opt"`
	SubscribeNearbyassistantSwitch   proto.Option[int32] `protobuf:"varint,42024,opt"`
	TorchbearerFlag                  proto.Option[int32] `protobuf:"varint,42051,opt"`
	PreloadDisableFlag               proto.Option[int32] `protobuf:"varint,42073,opt"`
	ReqMedalwallFlag                 proto.Option[int32] `protobuf:"varint,42075,opt"`
	NotifyOnLikeRankingListFlag      proto.Option[int32] `protobuf:"varint,42092,opt"`
	ApolloStatus                     proto.Option[int32] `protobuf:"varint,42980,opt"`
}
