// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/channel/MsgResponsesSvr.proto

package channel

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type BatchGetMsgRspCountReq struct {
	GuildMsgList []*GuildMsg `protobuf:"bytes,1,rep"`
}

type BatchGetMsgRspCountRsp struct {
	GuildMsgInfoList []*GuildMsgInfo `protobuf:"bytes,1,rep"`
}

type SvrChannelMsg struct {
	ChannelId proto.Option[uint64] `protobuf:"varint,1,opt"`
	Id        []*MsgId             `protobuf:"bytes,2,rep"`
}

type ChannelMsgInfo struct {
	ChannelId proto.Option[uint64] `protobuf:"varint,1,opt"`
	RespData  []*MsgRespData       `protobuf:"bytes,2,rep"`
}

type EmojiReaction struct {
	EmojiId        proto.Option[string] `protobuf:"bytes,1,opt"`
	EmojiType      proto.Option[uint64] `protobuf:"varint,2,opt"`
	Cnt            proto.Option[uint64] `protobuf:"varint,3,opt"`
	IsClicked      proto.Option[bool]   `protobuf:"varint,4,opt"`
	IsDefaultEmoji proto.Option[bool]   `protobuf:"varint,10001,opt"`
	_              [0]func()
}

type GuildMsg struct {
	GuildId        proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChannelMsgList []*SvrChannelMsg     `protobuf:"bytes,2,rep"`
}

type GuildMsgInfo struct {
	GuildId            proto.Option[uint64] `protobuf:"varint,1,opt"`
	ChannelMsgInfoList []*ChannelMsgInfo    `protobuf:"bytes,2,rep"`
}

type MsgCnt struct {
	Id            *MsgId           `protobuf:"bytes,1,opt"`
	EmojiReaction []*EmojiReaction `protobuf:"bytes,2,rep"`
}

type MsgId struct {
	Version proto.Option[uint64] `protobuf:"varint,1,opt"`
	Seq     proto.Option[uint64] `protobuf:"varint,2,opt"`
	_       [0]func()
}

type MsgRespData struct {
	Id  *MsgId `protobuf:"bytes,1,opt"`
	Cnt []byte `protobuf:"bytes,2,opt"`
}
