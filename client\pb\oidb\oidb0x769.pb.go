// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0x769.proto

package oidb

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type CPU struct {
	Model     proto.Option[string] `protobuf:"bytes,1,opt"`
	Cores     proto.Option[uint32] `protobuf:"varint,2,opt"`
	Frequency proto.Option[uint32] `protobuf:"varint,3,opt"`
	_         [0]func()
}

type Camera struct {
	Primary   proto.Option[uint64] `protobuf:"varint,1,opt"`
	Secondary proto.Option[uint64] `protobuf:"varint,2,opt"`
	Flash     proto.Option[bool]   `protobuf:"varint,3,opt"`
	_         [0]func()
}

type D769ConfigSeq struct {
	Type    proto.Option[uint32] `protobuf:"varint,1,opt"`
	Version proto.Option[uint32] `protobuf:"varint,2,opt"`
	_       [0]func()
}

type Content struct {
	TaskId   proto.Option[uint32] `protobuf:"varint,1,opt"`
	Compress proto.Option[uint32] `protobuf:"varint,2,opt"`
	Content  []byte               `protobuf:"bytes,10,opt"`
}

type D769DeviceInfo struct {
	Brand   proto.Option[string] `protobuf:"bytes,1,opt"`
	Model   proto.Option[string] `protobuf:"bytes,2,opt"`
	Os      *C41219OS            `protobuf:"bytes,3,opt"`
	Cpu     *CPU                 `protobuf:"bytes,4,opt"`
	Memory  *Memory              `protobuf:"bytes,5,opt"`
	Storage *Storage             `protobuf:"bytes,6,opt"`
	Screen  *Screen              `protobuf:"bytes,7,opt"`
	Camera  *Camera              `protobuf:"bytes,8,opt"`
	_       [0]func()
}

type Memory struct {
	Total   proto.Option[uint64] `protobuf:"varint,1,opt"`
	Process proto.Option[uint64] `protobuf:"varint,2,opt"`
	_       [0]func()
}

type C41219OS struct {
	Type    proto.Option[uint32] `protobuf:"varint,1,opt"`
	Version proto.Option[string] `protobuf:"bytes,2,opt"`
	Sdk     proto.Option[string] `protobuf:"bytes,3,opt"`
	Kernel  proto.Option[string] `protobuf:"bytes,4,opt"`
	Rom     proto.Option[string] `protobuf:"bytes,5,opt"`
	_       [0]func()
}

type QueryUinPackageUsageReq struct {
	Type        proto.Option[uint32] `protobuf:"varint,1,opt"`
	UinFileSize proto.Option[uint64] `protobuf:"varint,2,opt"`
	_           [0]func()
}

type QueryUinPackageUsageRsp struct {
	Status             proto.Option[uint32]  `protobuf:"varint,1,opt"`
	LeftUinNum         proto.Option[uint64]  `protobuf:"varint,2,opt"`
	MaxUinNum          proto.Option[uint64]  `protobuf:"varint,3,opt"`
	Proportion         proto.Option[uint32]  `protobuf:"varint,4,opt"`
	UinPackageUsedList []*UinPackageUsedInfo `protobuf:"bytes,10,rep"`
}

type D769ReqBody struct {
	ConfigList              []*D769ConfigSeq         `protobuf:"bytes,1,rep"`
	DeviceInfo              *D769DeviceInfo          `protobuf:"bytes,2,opt"`
	Info                    proto.Option[string]     `protobuf:"bytes,3,opt"`
	Province                proto.Option[string]     `protobuf:"bytes,4,opt"`
	City                    proto.Option[string]     `protobuf:"bytes,5,opt"`
	ReqDebugMsg             proto.Option[int32]      `protobuf:"varint,6,opt"`
	QueryUinPackageUsageReq *QueryUinPackageUsageReq `protobuf:"bytes,101,opt"`
}

type D769RspBody struct {
	Result                  proto.Option[uint32]     `protobuf:"varint,1,opt"`
	ConfigList              []*D769ConfigSeq         `protobuf:"bytes,2,rep"`
	QueryUinPackageUsageRsp *QueryUinPackageUsageRsp `protobuf:"bytes,101,opt"`
}

type Screen struct {
	Model      proto.Option[string] `protobuf:"bytes,1,opt"`
	Width      proto.Option[uint32] `protobuf:"varint,2,opt"`
	Height     proto.Option[uint32] `protobuf:"varint,3,opt"`
	Dpi        proto.Option[uint32] `protobuf:"varint,4,opt"`
	MultiTouch proto.Option[bool]   `protobuf:"varint,5,opt"`
	_          [0]func()
}

type Storage struct {
	Builtin  proto.Option[uint64] `protobuf:"varint,1,opt"`
	External proto.Option[uint64] `protobuf:"varint,2,opt"`
	_        [0]func()
}

type UinPackageUsedInfo struct {
	RuleId proto.Option[uint32] `protobuf:"varint,1,opt"`
	Author proto.Option[string] `protobuf:"bytes,2,opt"`
	Url    proto.Option[string] `protobuf:"bytes,3,opt"`
	UinNum proto.Option[uint64] `protobuf:"varint,4,opt"`
	_      [0]func()
}
