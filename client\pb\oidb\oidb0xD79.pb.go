// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0xD79.proto

package oidb

type D79ReqBody struct {
	Seq          uint64 `protobuf:"varint,1,opt"`
	Uin          uint64 `protobuf:"varint,2,opt"`
	CompressFlag uint32 `protobuf:"varint,3,opt"`
	Content      []byte `protobuf:"bytes,4,opt"`
	SenderUin    uint64 `protobuf:"varint,5,opt"`
	Qua          []byte `protobuf:"bytes,6,opt"`
	WordExt      []byte `protobuf:"bytes,7,opt"`
}

type D79RspBody struct {
	Ret          uint32      `protobuf:"varint,1,opt"`
	Seq          uint64      `protobuf:"varint,2,opt"`
	Uin          uint64      `protobuf:"varint,3,opt"`
	CompressFlag uint32      `protobuf:"varint,4,opt"`
	Content      *D79Content `protobuf:"bytes,5,opt"`
	_            [0]func()
}

type D79Content struct {
	SliceContent [][]byte `protobuf:"bytes,1,rep"`
}
