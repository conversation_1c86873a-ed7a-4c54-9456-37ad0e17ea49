// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/oidb/oidb0x6d6.proto

package oidb

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type DeleteFileReqBody struct {
	GroupCode      proto.Option[int64]  `protobuf:"varint,1,opt"`
	AppId          proto.Option[int32]  `protobuf:"varint,2,opt"`
	BusId          proto.Option[int32]  `protobuf:"varint,3,opt"`
	ParentFolderId proto.Option[string] `protobuf:"bytes,4,opt"`
	FileId         proto.Option[string] `protobuf:"bytes,5,opt"`
	_              [0]func()
}

type DeleteFileRspBody struct {
	RetCode       proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording proto.Option[string] `protobuf:"bytes,3,opt"`
	_             [0]func()
}

type DownloadFileReqBody struct {
	GroupCode        proto.Option[int64]  `protobuf:"varint,1,opt"`
	AppId            proto.Option[int32]  `protobuf:"varint,2,opt"`
	BusId            proto.Option[int32]  `protobuf:"varint,3,opt"`
	FileId           proto.Option[string] `protobuf:"bytes,4,opt"`
	BoolThumbnailReq proto.Option[bool]   `protobuf:"varint,5,opt"`
	UrlType          proto.Option[int32]  `protobuf:"varint,6,opt"`
	BoolPreviewReq   proto.Option[bool]   `protobuf:"varint,7,opt"`
	_                [0]func()
}

type DownloadFileRspBody struct {
	RetCode       proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording proto.Option[string] `protobuf:"bytes,3,opt"`
	DownloadIp    proto.Option[string] `protobuf:"bytes,4,opt"`
	DownloadDns   []byte               `protobuf:"bytes,5,opt"`
	DownloadUrl   []byte               `protobuf:"bytes,6,opt"`
	Sha           []byte               `protobuf:"bytes,7,opt"`
	Sha3          []byte               `protobuf:"bytes,8,opt"`
	Md5           []byte               `protobuf:"bytes,9,opt"`
	CookieVal     []byte               `protobuf:"bytes,10,opt"`
	SaveFileName  proto.Option[string] `protobuf:"bytes,11,opt"`
	PreviewPort   proto.Option[int32]  `protobuf:"varint,12,opt"`
}

type MoveFileReqBody struct {
	GroupCode      proto.Option[int64]  `protobuf:"varint,1,opt"`
	AppId          proto.Option[int32]  `protobuf:"varint,2,opt"`
	BusId          proto.Option[int32]  `protobuf:"varint,3,opt"`
	FileId         proto.Option[string] `protobuf:"bytes,4,opt"`
	ParentFolderId proto.Option[string] `protobuf:"bytes,5,opt"`
	DestFolderId   proto.Option[string] `protobuf:"bytes,6,opt"`
	_              [0]func()
}

type MoveFileRspBody struct {
	RetCode        proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg         proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording  proto.Option[string] `protobuf:"bytes,3,opt"`
	ParentFolderId proto.Option[string] `protobuf:"bytes,4,opt"`
	_              [0]func()
}

type RenameFileReqBody struct {
	GroupCode      proto.Option[int64]  `protobuf:"varint,1,opt"`
	AppId          proto.Option[int32]  `protobuf:"varint,2,opt"`
	BusId          proto.Option[int32]  `protobuf:"varint,3,opt"`
	FileId         proto.Option[string] `protobuf:"bytes,4,opt"`
	ParentFolderId proto.Option[string] `protobuf:"bytes,5,opt"`
	NewFileName    proto.Option[string] `protobuf:"bytes,6,opt"`
	_              [0]func()
}

type RenameFileRspBody struct {
	RetCode       proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording proto.Option[string] `protobuf:"bytes,3,opt"`
	_             [0]func()
}

type D6D6ReqBody struct {
	UploadFileReq *UploadFileReqBody `protobuf:"bytes,1,opt"`
	// optional ResendReqBody resendFileReq = 2;
	DownloadFileReq *DownloadFileReqBody `protobuf:"bytes,3,opt"`
	DeleteFileReq   *DeleteFileReqBody   `protobuf:"bytes,4,opt"`
	RenameFileReq   *RenameFileReqBody   `protobuf:"bytes,5,opt"`
	MoveFileReq     *MoveFileReqBody     `protobuf:"bytes,6,opt"`
	_               [0]func()
}

type ResendReqBody struct {
	GroupCode proto.Option[int64]  `protobuf:"varint,1,opt"`
	AppId     proto.Option[int32]  `protobuf:"varint,2,opt"`
	BusId     proto.Option[int32]  `protobuf:"varint,3,opt"`
	FileId    proto.Option[string] `protobuf:"bytes,4,opt"`
	Sha       []byte               `protobuf:"bytes,5,opt"`
}

type ResendRspBody struct {
	RetCode       proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording proto.Option[string] `protobuf:"bytes,3,opt"`
	UploadIp      proto.Option[string] `protobuf:"bytes,4,opt"`
	FileKey       []byte               `protobuf:"bytes,5,opt"`
	CheckKey      []byte               `protobuf:"bytes,6,opt"`
}

type D6D6RspBody struct {
	UploadFileRsp   *UploadFileRspBody   `protobuf:"bytes,1,opt"`
	ResendFileRsp   *ResendRspBody       `protobuf:"bytes,2,opt"`
	DownloadFileRsp *DownloadFileRspBody `protobuf:"bytes,3,opt"`
	DeleteFileRsp   *DeleteFileRspBody   `protobuf:"bytes,4,opt"`
	RenameFileRsp   *RenameFileRspBody   `protobuf:"bytes,5,opt"`
	MoveFileRsp     *MoveFileRspBody     `protobuf:"bytes,6,opt"`
	_               [0]func()
}

type UploadFileReqBody struct {
	GroupCode          proto.Option[int64]  `protobuf:"varint,1,opt"`
	AppId              proto.Option[int32]  `protobuf:"varint,2,opt"`
	BusId              proto.Option[int32]  `protobuf:"varint,3,opt"`
	Entrance           proto.Option[int32]  `protobuf:"varint,4,opt"`
	ParentFolderId     proto.Option[string] `protobuf:"bytes,5,opt"`
	FileName           proto.Option[string] `protobuf:"bytes,6,opt"`
	LocalPath          proto.Option[string] `protobuf:"bytes,7,opt"`
	Int64FileSize      proto.Option[int64]  `protobuf:"varint,8,opt"`
	Sha                []byte               `protobuf:"bytes,9,opt"`
	Sha3               []byte               `protobuf:"bytes,10,opt"`
	Md5                []byte               `protobuf:"bytes,11,opt"`
	SupportMultiUpload proto.Option[bool]   `protobuf:"varint,15,opt"`
}

type UploadFileRspBody struct {
	RetCode       proto.Option[int32]  `protobuf:"varint,1,opt"`
	RetMsg        proto.Option[string] `protobuf:"bytes,2,opt"`
	ClientWording proto.Option[string] `protobuf:"bytes,3,opt"`
	UploadIp      proto.Option[string] `protobuf:"bytes,4,opt"`
	ServerDns     proto.Option[string] `protobuf:"bytes,5,opt"`
	BusId         proto.Option[int32]  `protobuf:"varint,6,opt"`
	FileId        proto.Option[string] `protobuf:"bytes,7,opt"`
	FileKey       []byte               `protobuf:"bytes,8,opt"`
	CheckKey      []byte               `protobuf:"bytes,9,opt"`
	BoolFileExist proto.Option[bool]   `protobuf:"varint,10,opt"`
	UploadIpLanV4 []string             `protobuf:"bytes,12,rep"`
	UploadIpLanV6 []string             `protobuf:"bytes,13,rep"`
	UploadPort    proto.Option[int32]  `protobuf:"varint,14,opt"`
}
