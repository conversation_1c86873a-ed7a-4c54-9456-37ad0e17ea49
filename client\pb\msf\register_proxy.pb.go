// Code generated by protoc-gen-golite. DO NOT EDIT.
// source: pb/msf/register_proxy.proto

package msf

import (
	proto "github.com/RomiChan/protobuf/proto"
)

type DiscussList struct {
	DiscussCode proto.Option[uint64] `protobuf:"varint,1,opt"`
	DiscussSeq  proto.Option[uint64] `protobuf:"varint,2,opt"`
	MemberSeq   proto.Option[uint64] `protobuf:"varint,3,opt"`
	InfoSeq     proto.Option[uint64] `protobuf:"varint,4,opt"`
	BHotGroup   proto.Option[bool]   `protobuf:"varint,5,opt"`
	RedpackTime proto.Option[uint64] `protobuf:"varint,6,opt"`
	HasMsg      proto.Option[bool]   `protobuf:"varint,7,opt"`
	DicussFlag  proto.Option[int64]  `protobuf:"varint,8,opt"`
	_           [0]func()
}

type GroupList struct {
	GroupCode             proto.Option[uint64] `protobuf:"varint,1,opt"`
	GroupSeq              proto.Option[uint64] `protobuf:"varint,2,opt"`
	MemberSeq             proto.Option[uint64] `protobuf:"varint,3,opt"`
	Mask                  proto.Option[uint64] `protobuf:"varint,4,opt"`
	RedpackTime           proto.Option[uint64] `protobuf:"varint,5,opt"`
	HasMsg                proto.Option[bool]   `protobuf:"varint,6,opt"`
	GroupFlag             proto.Option[int64]  `protobuf:"varint,7,opt"`
	GroupType             proto.Option[uint64] `protobuf:"varint,8,opt"`
	GroupNameSeq          proto.Option[uint32] `protobuf:"varint,9,opt"`
	GroupMemberSeq        proto.Option[uint32] `protobuf:"varint,10,opt"`
	UinFlagEx2            proto.Option[uint32] `protobuf:"varint,11,opt"`
	ImportantMsgLatestSeq proto.Option[uint32] `protobuf:"varint,12,opt"`
	_                     [0]func()
}

type SvcPbResponsePullDisMsgProxy struct {
	MemberSeq proto.Option[uint64] `protobuf:"varint,1,opt"`
	Content   []byte               `protobuf:"bytes,2,opt"`
}

type SvcRegisterProxyMsgResp struct {
	Result proto.Option[uint32] `protobuf:"varint,1,opt"`
	ErrMsg []byte               `protobuf:"bytes,2,opt"`
	Flag   proto.Option[uint32] `protobuf:"varint,3,opt"`
	Seq    proto.Option[uint32] `protobuf:"varint,4,opt"`
	Info   *SvcResponseMsgInfo  `protobuf:"bytes,5,opt"`
	// repeated GroupList groupList = 6;
	// repeated DiscussList discussList = 7;
	GroupMsg []*SvcResponsePbPullGroupMsgProxy `protobuf:"bytes,8,rep"`
	// repeated SvcPbResponsePullDisMsgProxy discussMsg = 9;
	C2CMsg          []byte               `protobuf:"bytes,10,opt"`
	PubAccountMsg   []byte               `protobuf:"bytes,11,opt"`
	DiscussListFlag proto.Option[uint32] `protobuf:"varint,12,opt"`
}

type SvcResponseMsgInfo struct {
	GroupNum   proto.Option[uint32] `protobuf:"varint,1,opt"`
	DiscussNum proto.Option[uint32] `protobuf:"varint,2,opt"`
	_          [0]func()
}

type SvcResponsePbPullGroupMsgProxy struct {
	MemberSeq proto.Option[uint64] `protobuf:"varint,1,opt"`
	Content   []byte               `protobuf:"bytes,2,opt"`
}
