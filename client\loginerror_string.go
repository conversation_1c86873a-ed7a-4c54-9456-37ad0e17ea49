// Code generated by "stringer -type=LoginError"; DO NOT EDIT.

package client

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[NeedCaptcha-1]
	_ = x[OtherLoginError-3]
	_ = x[UnsafeDeviceError-4]
	_ = x[SMSNeededError-5]
	_ = x[TooManySMSRequestError-6]
	_ = x[SMSOrVerifyNeededError-7]
	_ = x[SliderNeededError-8]
	_ = x[UnknownLoginError - -1]
}

const (
	_LoginError_name_0 = "UnknownLoginError"
	_LoginError_name_1 = "NeedCaptcha"
	_LoginError_name_2 = "OtherLoginErrorUnsafeDeviceErrorSMSNeededErrorTooManySMSRequestErrorSMSOrVerifyNeededErrorSliderNeededError"
)

var (
	_LoginError_index_2 = [...]uint8{0, 15, 32, 46, 68, 90, 107}
)

func (i LoginError) String() string {
	switch {
	case i == -1:
		return _LoginError_name_0
	case i == 1:
		return _LoginError_name_1
	case 3 <= i && i <= 8:
		i -= 3
		return _LoginError_name_2[_LoginError_index_2[i]:_LoginError_index_2[i+1]]
	default:
		return "LoginError(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
